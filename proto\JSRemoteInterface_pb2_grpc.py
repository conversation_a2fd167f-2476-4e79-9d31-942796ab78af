# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import JSRemoteInterface_pb2 as JSRemoteInterface__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in JSRemoteInterface_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class JSRemoteControlStub(object):
    """Interface that USIM Service support 
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.getSimStateStream = channel.unary_stream(
                '/JSRemoteInterface.JSRemoteControl/getSimStateStream',
                request_serializer=JSRemoteInterface__pb2.SimStateTypes.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.SimState.FromString,
                _registered_method=True)
        self.getEntityTrackList = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/getEntityTrackList',
                request_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.TrackList.FromString,
                _registered_method=True)
        self.getDamageState = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/getDamageState',
                request_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.DamageState.FromString,
                _registered_method=True)
        self.getEntityResourceData = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/getEntityResourceData',
                request_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResourceDataList.FromString,
                _registered_method=True)
        self.getDestroyedEntityList = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/getDestroyedEntityList',
                request_serializer=JSRemoteInterface__pb2.EmptyMessage.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.EntityIdList.FromString,
                _registered_method=True)
        self.getEntityDescriptionById = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/getEntityDescriptionById',
                request_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.EntityTypeDescription.FromString,
                _registered_method=True)
        self.doesChordHitTerrain = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/doesChordHitTerrain',
                request_serializer=JSRemoteInterface__pb2.Points.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.Visibility.FromString,
                _registered_method=True)
        self.getAeraTerrainHeight = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/getAeraTerrainHeight',
                request_serializer=JSRemoteInterface__pb2.AreaTerrainHeightRequest.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.AreaTerrainHeightResponse.FromString,
                _registered_method=True)
        self.loadScenario = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/loadScenario',
                request_serializer=JSRemoteInterface__pb2.ScenarioPath.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.closeScenario = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/closeScenario',
                request_serializer=JSRemoteInterface__pb2.EmptyParam.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.run = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/run',
                request_serializer=JSRemoteInterface__pb2.ScenarioPath.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.pause = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/pause',
                request_serializer=JSRemoteInterface__pb2.PauseParm.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.rewind = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/rewind',
                request_serializer=JSRemoteInterface__pb2.ScenarioPath.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.setTimeMultiplier = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/setTimeMultiplier',
                request_serializer=JSRemoteInterface__pb2.TimeMultiplier.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.createRoute = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/createRoute',
                request_serializer=JSRemoteInterface__pb2.Route.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.createPhaseLine = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/createPhaseLine',
                request_serializer=JSRemoteInterface__pb2.PhaseLine.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.createControlArea = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/createControlArea',
                request_serializer=JSRemoteInterface__pb2.ControlArea.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.createEntity = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/createEntity',
                request_serializer=JSRemoteInterface__pb2.EntitySpawnParam.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                _registered_method=True)
        self.removeEntity = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/removeEntity',
                request_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.systemEnable = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/systemEnable',
                request_serializer=JSRemoteInterface__pb2.SystemEnableSet.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.setSystemRotationSpeed = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/setSystemRotationSpeed',
                request_serializer=JSRemoteInterface__pb2.SystemRotationTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.setSystemRotationAngle = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/setSystemRotationAngle',
                request_serializer=JSRemoteInterface__pb2.SystemRotationTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.setTrackTarget = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/setTrackTarget',
                request_serializer=JSRemoteInterface__pb2.TrackTargetTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.getTrackTarget = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/getTrackTarget',
                request_serializer=JSRemoteInterface__pb2.TrackTargetQuery.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.TrackTargetList.FromString,
                _registered_method=True)
        self.stopMove = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/stopMove',
                request_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.fixwingTakeOff = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/fixwingTakeOff',
                request_serializer=JSRemoteInterface__pb2.RouteTaskInfo.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.fixwingLand = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/fixwingLand',
                request_serializer=JSRemoteInterface__pb2.RouteTaskInfo.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.verticalLand = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/verticalLand',
                request_serializer=JSRemoteInterface__pb2.moveToLocationTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.moveAlongRoute = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/moveAlongRoute',
                request_serializer=JSRemoteInterface__pb2.RouteTaskInfo.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.moveToLocation = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/moveToLocation',
                request_serializer=JSRemoteInterface__pb2.moveToLocationTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.followEntity = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/followEntity',
                request_serializer=JSRemoteInterface__pb2.FollowEntityTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.flyHeadingAndAltitude = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/flyHeadingAndAltitude',
                request_serializer=JSRemoteInterface__pb2.FlyHeadingAndAltitudeTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.flyOrbit = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/flyOrbit',
                request_serializer=JSRemoteInterface__pb2.OrbitTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.setOrderedSpeed = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/setOrderedSpeed',
                request_serializer=JSRemoteInterface__pb2.SpeedSet.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.MoverSpeedCtrl = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/MoverSpeedCtrl',
                request_serializer=JSRemoteInterface__pb2.MoverSpeedCtrlSet.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.laseTarget = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/laseTarget',
                request_serializer=JSRemoteInterface__pb2.TargetTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.setLaseCode = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/setLaseCode',
                request_serializer=JSRemoteInterface__pb2.LaseCodeSet.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.syncLaseCode = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/syncLaseCode',
                request_serializer=JSRemoteInterface__pb2.TargetTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.closeLaserDesignator = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/closeLaserDesignator',
                request_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.addJammerTarget = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/addJammerTarget',
                request_serializer=JSRemoteInterface__pb2.TargetTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.removeJammerTarget = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/removeJammerTarget',
                request_serializer=JSRemoteInterface__pb2.TargetTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.GetJammingTargets = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/GetJammingTargets',
                request_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.JammingTargets.FromString,
                _registered_method=True)
        self.fireAtTarget = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/fireAtTarget',
                request_serializer=JSRemoteInterface__pb2.FireAtTargetTask.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.launchExpendableResource = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/launchExpendableResource',
                request_serializer=JSRemoteInterface__pb2.LaunchResource.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.ffeTarget = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/ffeTarget',
                request_serializer=JSRemoteInterface__pb2.ffeTargetParam.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.getWeaponResouce = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/getWeaponResouce',
                request_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.WeaponResourceList.FromString,
                _registered_method=True)
        self.setSensorWorkMode = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/setSensorWorkMode',
                request_serializer=JSRemoteInterface__pb2.SystemWorkMode.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.getSensorWorkMode = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/getSensorWorkMode',
                request_serializer=JSRemoteInterface__pb2.SystemWorkMode.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.SystemWorkMode.FromString,
                _registered_method=True)
        self.setSensorParam = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/setSensorParam',
                request_serializer=JSRemoteInterface__pb2.SysWorkModeParam.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)
        self.GetSensorParam = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/GetSensorParam',
                request_serializer=JSRemoteInterface__pb2.SysWorkModeParam.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.SysWorkModeParam.FromString,
                _registered_method=True)
        self.showMessage = channel.unary_unary(
                '/JSRemoteInterface.JSRemoteControl/showMessage',
                request_serializer=JSRemoteInterface__pb2.TextMessage.SerializeToString,
                response_deserializer=JSRemoteInterface__pb2.ResultCode.FromString,
                _registered_method=True)


class JSRemoteControlServicer(object):
    """Interface that USIM Service support 
    """

    def getSimStateStream(self, request, context):
        """/////////////////////////////////////////stream data 
        1.1 Gloabl Sim Data
        1) SimState (1time per second)  
        2) Simulation events: fire, explosion, destruction, communication
        3) States of all simulation entities in the simulation (basic information)
        4) Working states of all detectors in the simulation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getEntityTrackList(self, request, context):
        """/////////////////////////////////////////State query
        2.1 Targets detected by the entity's sensors
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getDamageState(self, request, context):
        """2.2 Query damage status
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getEntityResourceData(self, request, context):
        """2.3 Query entity resource data
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getDestroyedEntityList(self, request, context):
        """2.4  
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getEntityDescriptionById(self, request, context):
        """2.5
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def doesChordHitTerrain(self, request, context):
        """/////////////////////////Terrain
        2.6 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getAeraTerrainHeight(self, request, context):
        """2.7 Elevation data of the specified area
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def loadScenario(self, request, context):
        """///////////////////////////////////////// Simulation Control
        3.1 Load scenario
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def closeScenario(self, request, context):
        """3.2 Close scenario
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def run(self, request, context):
        """3.3 Start running
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def pause(self, request, context):
        """3.4 Pause
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def rewind(self, request, context):
        """3.5 Reset scenario
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def setTimeMultiplier(self, request, context):
        """3.6 Set simulation speed multiplier
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createRoute(self, request, context):
        """/////////////////////////////////////////Create Something
        4.1. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createPhaseLine(self, request, context):
        """4.2. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createControlArea(self, request, context):
        """4.3. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createEntity(self, request, context):
        """4.4. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def removeEntity(self, request, context):
        """4.5. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def systemEnable(self, request, context):
        """////////////////////////////////////////General System Control
        5.1
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def setSystemRotationSpeed(self, request, context):
        """5.2  Only Work On ArticularPart System
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def setSystemRotationAngle(self, request, context):
        """5.3. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def setTrackTarget(self, request, context):
        """5.4 start or stop track target
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getTrackTarget(self, request, context):
        """5.5
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def stopMove(self, request, context):
        """///////////////////////////////////////// Mover Control
        6.1. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def fixwingTakeOff(self, request, context):
        """6.2. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def fixwingLand(self, request, context):
        """6.3. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def verticalLand(self, request, context):
        """6.4. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def moveAlongRoute(self, request, context):
        """6.5. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def moveToLocation(self, request, context):
        """6.6. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def followEntity(self, request, context):
        """6.7.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def flyHeadingAndAltitude(self, request, context):
        """6.8. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def flyOrbit(self, request, context):
        """6.9. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def setOrderedSpeed(self, request, context):
        """6.10. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MoverSpeedCtrl(self, request, context):
        """6.11. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def laseTarget(self, request, context):
        """////////////////////////////////Laser Control
        7.1 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def setLaseCode(self, request, context):
        """7.2
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def syncLaseCode(self, request, context):
        """7.3. Synchronize laser code with a certain entity (enable laser guidance for other entities)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def closeLaserDesignator(self, request, context):
        """7.4. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def addJammerTarget(self, request, context):
        """//////////////////////////////Jammer Control
        8.1 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def removeJammerTarget(self, request, context):
        """8.2 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetJammingTargets(self, request, context):
        """8.3 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def fireAtTarget(self, request, context):
        """///////////////////////////Weapon Control
        9.1. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def launchExpendableResource(self, request, context):
        """9.2. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ffeTarget(self, request, context):
        """9.3. 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getWeaponResouce(self, request, context):
        """9.4 Weapon working status of the entity
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def setSensorWorkMode(self, request, context):
        """/////////////////////////////////////General Sensor Control
        10.1
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getSensorWorkMode(self, request, context):
        """10.2
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def setSensorParam(self, request, context):
        """10.3
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSensorParam(self, request, context):
        """10.4
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def showMessage(self, request, context):
        """///////////////////////////////////////////EW Control

        ///////////////////////////////////////// UI
        11.1
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_JSRemoteControlServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'getSimStateStream': grpc.unary_stream_rpc_method_handler(
                    servicer.getSimStateStream,
                    request_deserializer=JSRemoteInterface__pb2.SimStateTypes.FromString,
                    response_serializer=JSRemoteInterface__pb2.SimState.SerializeToString,
            ),
            'getEntityTrackList': grpc.unary_unary_rpc_method_handler(
                    servicer.getEntityTrackList,
                    request_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                    response_serializer=JSRemoteInterface__pb2.TrackList.SerializeToString,
            ),
            'getDamageState': grpc.unary_unary_rpc_method_handler(
                    servicer.getDamageState,
                    request_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                    response_serializer=JSRemoteInterface__pb2.DamageState.SerializeToString,
            ),
            'getEntityResourceData': grpc.unary_unary_rpc_method_handler(
                    servicer.getEntityResourceData,
                    request_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResourceDataList.SerializeToString,
            ),
            'getDestroyedEntityList': grpc.unary_unary_rpc_method_handler(
                    servicer.getDestroyedEntityList,
                    request_deserializer=JSRemoteInterface__pb2.EmptyMessage.FromString,
                    response_serializer=JSRemoteInterface__pb2.EntityIdList.SerializeToString,
            ),
            'getEntityDescriptionById': grpc.unary_unary_rpc_method_handler(
                    servicer.getEntityDescriptionById,
                    request_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                    response_serializer=JSRemoteInterface__pb2.EntityTypeDescription.SerializeToString,
            ),
            'doesChordHitTerrain': grpc.unary_unary_rpc_method_handler(
                    servicer.doesChordHitTerrain,
                    request_deserializer=JSRemoteInterface__pb2.Points.FromString,
                    response_serializer=JSRemoteInterface__pb2.Visibility.SerializeToString,
            ),
            'getAeraTerrainHeight': grpc.unary_unary_rpc_method_handler(
                    servicer.getAeraTerrainHeight,
                    request_deserializer=JSRemoteInterface__pb2.AreaTerrainHeightRequest.FromString,
                    response_serializer=JSRemoteInterface__pb2.AreaTerrainHeightResponse.SerializeToString,
            ),
            'loadScenario': grpc.unary_unary_rpc_method_handler(
                    servicer.loadScenario,
                    request_deserializer=JSRemoteInterface__pb2.ScenarioPath.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'closeScenario': grpc.unary_unary_rpc_method_handler(
                    servicer.closeScenario,
                    request_deserializer=JSRemoteInterface__pb2.EmptyParam.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'run': grpc.unary_unary_rpc_method_handler(
                    servicer.run,
                    request_deserializer=JSRemoteInterface__pb2.ScenarioPath.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'pause': grpc.unary_unary_rpc_method_handler(
                    servicer.pause,
                    request_deserializer=JSRemoteInterface__pb2.PauseParm.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'rewind': grpc.unary_unary_rpc_method_handler(
                    servicer.rewind,
                    request_deserializer=JSRemoteInterface__pb2.ScenarioPath.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'setTimeMultiplier': grpc.unary_unary_rpc_method_handler(
                    servicer.setTimeMultiplier,
                    request_deserializer=JSRemoteInterface__pb2.TimeMultiplier.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'createRoute': grpc.unary_unary_rpc_method_handler(
                    servicer.createRoute,
                    request_deserializer=JSRemoteInterface__pb2.Route.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'createPhaseLine': grpc.unary_unary_rpc_method_handler(
                    servicer.createPhaseLine,
                    request_deserializer=JSRemoteInterface__pb2.PhaseLine.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'createControlArea': grpc.unary_unary_rpc_method_handler(
                    servicer.createControlArea,
                    request_deserializer=JSRemoteInterface__pb2.ControlArea.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'createEntity': grpc.unary_unary_rpc_method_handler(
                    servicer.createEntity,
                    request_deserializer=JSRemoteInterface__pb2.EntitySpawnParam.FromString,
                    response_serializer=JSRemoteInterface__pb2.EntityID.SerializeToString,
            ),
            'removeEntity': grpc.unary_unary_rpc_method_handler(
                    servicer.removeEntity,
                    request_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'systemEnable': grpc.unary_unary_rpc_method_handler(
                    servicer.systemEnable,
                    request_deserializer=JSRemoteInterface__pb2.SystemEnableSet.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'setSystemRotationSpeed': grpc.unary_unary_rpc_method_handler(
                    servicer.setSystemRotationSpeed,
                    request_deserializer=JSRemoteInterface__pb2.SystemRotationTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'setSystemRotationAngle': grpc.unary_unary_rpc_method_handler(
                    servicer.setSystemRotationAngle,
                    request_deserializer=JSRemoteInterface__pb2.SystemRotationTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'setTrackTarget': grpc.unary_unary_rpc_method_handler(
                    servicer.setTrackTarget,
                    request_deserializer=JSRemoteInterface__pb2.TrackTargetTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'getTrackTarget': grpc.unary_unary_rpc_method_handler(
                    servicer.getTrackTarget,
                    request_deserializer=JSRemoteInterface__pb2.TrackTargetQuery.FromString,
                    response_serializer=JSRemoteInterface__pb2.TrackTargetList.SerializeToString,
            ),
            'stopMove': grpc.unary_unary_rpc_method_handler(
                    servicer.stopMove,
                    request_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'fixwingTakeOff': grpc.unary_unary_rpc_method_handler(
                    servicer.fixwingTakeOff,
                    request_deserializer=JSRemoteInterface__pb2.RouteTaskInfo.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'fixwingLand': grpc.unary_unary_rpc_method_handler(
                    servicer.fixwingLand,
                    request_deserializer=JSRemoteInterface__pb2.RouteTaskInfo.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'verticalLand': grpc.unary_unary_rpc_method_handler(
                    servicer.verticalLand,
                    request_deserializer=JSRemoteInterface__pb2.moveToLocationTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'moveAlongRoute': grpc.unary_unary_rpc_method_handler(
                    servicer.moveAlongRoute,
                    request_deserializer=JSRemoteInterface__pb2.RouteTaskInfo.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'moveToLocation': grpc.unary_unary_rpc_method_handler(
                    servicer.moveToLocation,
                    request_deserializer=JSRemoteInterface__pb2.moveToLocationTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'followEntity': grpc.unary_unary_rpc_method_handler(
                    servicer.followEntity,
                    request_deserializer=JSRemoteInterface__pb2.FollowEntityTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'flyHeadingAndAltitude': grpc.unary_unary_rpc_method_handler(
                    servicer.flyHeadingAndAltitude,
                    request_deserializer=JSRemoteInterface__pb2.FlyHeadingAndAltitudeTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'flyOrbit': grpc.unary_unary_rpc_method_handler(
                    servicer.flyOrbit,
                    request_deserializer=JSRemoteInterface__pb2.OrbitTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'setOrderedSpeed': grpc.unary_unary_rpc_method_handler(
                    servicer.setOrderedSpeed,
                    request_deserializer=JSRemoteInterface__pb2.SpeedSet.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'MoverSpeedCtrl': grpc.unary_unary_rpc_method_handler(
                    servicer.MoverSpeedCtrl,
                    request_deserializer=JSRemoteInterface__pb2.MoverSpeedCtrlSet.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'laseTarget': grpc.unary_unary_rpc_method_handler(
                    servicer.laseTarget,
                    request_deserializer=JSRemoteInterface__pb2.TargetTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'setLaseCode': grpc.unary_unary_rpc_method_handler(
                    servicer.setLaseCode,
                    request_deserializer=JSRemoteInterface__pb2.LaseCodeSet.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'syncLaseCode': grpc.unary_unary_rpc_method_handler(
                    servicer.syncLaseCode,
                    request_deserializer=JSRemoteInterface__pb2.TargetTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'closeLaserDesignator': grpc.unary_unary_rpc_method_handler(
                    servicer.closeLaserDesignator,
                    request_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'addJammerTarget': grpc.unary_unary_rpc_method_handler(
                    servicer.addJammerTarget,
                    request_deserializer=JSRemoteInterface__pb2.TargetTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'removeJammerTarget': grpc.unary_unary_rpc_method_handler(
                    servicer.removeJammerTarget,
                    request_deserializer=JSRemoteInterface__pb2.TargetTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'GetJammingTargets': grpc.unary_unary_rpc_method_handler(
                    servicer.GetJammingTargets,
                    request_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                    response_serializer=JSRemoteInterface__pb2.JammingTargets.SerializeToString,
            ),
            'fireAtTarget': grpc.unary_unary_rpc_method_handler(
                    servicer.fireAtTarget,
                    request_deserializer=JSRemoteInterface__pb2.FireAtTargetTask.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'launchExpendableResource': grpc.unary_unary_rpc_method_handler(
                    servicer.launchExpendableResource,
                    request_deserializer=JSRemoteInterface__pb2.LaunchResource.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'ffeTarget': grpc.unary_unary_rpc_method_handler(
                    servicer.ffeTarget,
                    request_deserializer=JSRemoteInterface__pb2.ffeTargetParam.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'getWeaponResouce': grpc.unary_unary_rpc_method_handler(
                    servicer.getWeaponResouce,
                    request_deserializer=JSRemoteInterface__pb2.EntityID.FromString,
                    response_serializer=JSRemoteInterface__pb2.WeaponResourceList.SerializeToString,
            ),
            'setSensorWorkMode': grpc.unary_unary_rpc_method_handler(
                    servicer.setSensorWorkMode,
                    request_deserializer=JSRemoteInterface__pb2.SystemWorkMode.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'getSensorWorkMode': grpc.unary_unary_rpc_method_handler(
                    servicer.getSensorWorkMode,
                    request_deserializer=JSRemoteInterface__pb2.SystemWorkMode.FromString,
                    response_serializer=JSRemoteInterface__pb2.SystemWorkMode.SerializeToString,
            ),
            'setSensorParam': grpc.unary_unary_rpc_method_handler(
                    servicer.setSensorParam,
                    request_deserializer=JSRemoteInterface__pb2.SysWorkModeParam.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
            'GetSensorParam': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSensorParam,
                    request_deserializer=JSRemoteInterface__pb2.SysWorkModeParam.FromString,
                    response_serializer=JSRemoteInterface__pb2.SysWorkModeParam.SerializeToString,
            ),
            'showMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.showMessage,
                    request_deserializer=JSRemoteInterface__pb2.TextMessage.FromString,
                    response_serializer=JSRemoteInterface__pb2.ResultCode.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'JSRemoteInterface.JSRemoteControl', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('JSRemoteInterface.JSRemoteControl', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class JSRemoteControl(object):
    """Interface that USIM Service support 
    """

    @staticmethod
    def getSimStateStream(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getSimStateStream',
            JSRemoteInterface__pb2.SimStateTypes.SerializeToString,
            JSRemoteInterface__pb2.SimState.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getEntityTrackList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getEntityTrackList',
            JSRemoteInterface__pb2.EntityID.SerializeToString,
            JSRemoteInterface__pb2.TrackList.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getDamageState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getDamageState',
            JSRemoteInterface__pb2.EntityID.SerializeToString,
            JSRemoteInterface__pb2.DamageState.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getEntityResourceData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getEntityResourceData',
            JSRemoteInterface__pb2.EntityID.SerializeToString,
            JSRemoteInterface__pb2.ResourceDataList.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getDestroyedEntityList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getDestroyedEntityList',
            JSRemoteInterface__pb2.EmptyMessage.SerializeToString,
            JSRemoteInterface__pb2.EntityIdList.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getEntityDescriptionById(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getEntityDescriptionById',
            JSRemoteInterface__pb2.EntityID.SerializeToString,
            JSRemoteInterface__pb2.EntityTypeDescription.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def doesChordHitTerrain(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/doesChordHitTerrain',
            JSRemoteInterface__pb2.Points.SerializeToString,
            JSRemoteInterface__pb2.Visibility.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getAeraTerrainHeight(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getAeraTerrainHeight',
            JSRemoteInterface__pb2.AreaTerrainHeightRequest.SerializeToString,
            JSRemoteInterface__pb2.AreaTerrainHeightResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def loadScenario(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/loadScenario',
            JSRemoteInterface__pb2.ScenarioPath.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def closeScenario(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/closeScenario',
            JSRemoteInterface__pb2.EmptyParam.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def run(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/run',
            JSRemoteInterface__pb2.ScenarioPath.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def pause(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/pause',
            JSRemoteInterface__pb2.PauseParm.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def rewind(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/rewind',
            JSRemoteInterface__pb2.ScenarioPath.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def setTimeMultiplier(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/setTimeMultiplier',
            JSRemoteInterface__pb2.TimeMultiplier.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createRoute(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/createRoute',
            JSRemoteInterface__pb2.Route.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createPhaseLine(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/createPhaseLine',
            JSRemoteInterface__pb2.PhaseLine.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createControlArea(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/createControlArea',
            JSRemoteInterface__pb2.ControlArea.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createEntity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/createEntity',
            JSRemoteInterface__pb2.EntitySpawnParam.SerializeToString,
            JSRemoteInterface__pb2.EntityID.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def removeEntity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/removeEntity',
            JSRemoteInterface__pb2.EntityID.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def systemEnable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/systemEnable',
            JSRemoteInterface__pb2.SystemEnableSet.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def setSystemRotationSpeed(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/setSystemRotationSpeed',
            JSRemoteInterface__pb2.SystemRotationTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def setSystemRotationAngle(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/setSystemRotationAngle',
            JSRemoteInterface__pb2.SystemRotationTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def setTrackTarget(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/setTrackTarget',
            JSRemoteInterface__pb2.TrackTargetTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getTrackTarget(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getTrackTarget',
            JSRemoteInterface__pb2.TrackTargetQuery.SerializeToString,
            JSRemoteInterface__pb2.TrackTargetList.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def stopMove(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/stopMove',
            JSRemoteInterface__pb2.EntityID.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def fixwingTakeOff(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/fixwingTakeOff',
            JSRemoteInterface__pb2.RouteTaskInfo.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def fixwingLand(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/fixwingLand',
            JSRemoteInterface__pb2.RouteTaskInfo.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def verticalLand(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/verticalLand',
            JSRemoteInterface__pb2.moveToLocationTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def moveAlongRoute(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/moveAlongRoute',
            JSRemoteInterface__pb2.RouteTaskInfo.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def moveToLocation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/moveToLocation',
            JSRemoteInterface__pb2.moveToLocationTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def followEntity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/followEntity',
            JSRemoteInterface__pb2.FollowEntityTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def flyHeadingAndAltitude(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/flyHeadingAndAltitude',
            JSRemoteInterface__pb2.FlyHeadingAndAltitudeTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def flyOrbit(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/flyOrbit',
            JSRemoteInterface__pb2.OrbitTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def setOrderedSpeed(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/setOrderedSpeed',
            JSRemoteInterface__pb2.SpeedSet.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MoverSpeedCtrl(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/MoverSpeedCtrl',
            JSRemoteInterface__pb2.MoverSpeedCtrlSet.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def laseTarget(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/laseTarget',
            JSRemoteInterface__pb2.TargetTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def setLaseCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/setLaseCode',
            JSRemoteInterface__pb2.LaseCodeSet.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def syncLaseCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/syncLaseCode',
            JSRemoteInterface__pb2.TargetTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def closeLaserDesignator(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/closeLaserDesignator',
            JSRemoteInterface__pb2.EntityID.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def addJammerTarget(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/addJammerTarget',
            JSRemoteInterface__pb2.TargetTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def removeJammerTarget(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/removeJammerTarget',
            JSRemoteInterface__pb2.TargetTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetJammingTargets(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/GetJammingTargets',
            JSRemoteInterface__pb2.EntityID.SerializeToString,
            JSRemoteInterface__pb2.JammingTargets.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def fireAtTarget(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/fireAtTarget',
            JSRemoteInterface__pb2.FireAtTargetTask.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def launchExpendableResource(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/launchExpendableResource',
            JSRemoteInterface__pb2.LaunchResource.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ffeTarget(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/ffeTarget',
            JSRemoteInterface__pb2.ffeTargetParam.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getWeaponResouce(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getWeaponResouce',
            JSRemoteInterface__pb2.EntityID.SerializeToString,
            JSRemoteInterface__pb2.WeaponResourceList.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def setSensorWorkMode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/setSensorWorkMode',
            JSRemoteInterface__pb2.SystemWorkMode.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getSensorWorkMode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/getSensorWorkMode',
            JSRemoteInterface__pb2.SystemWorkMode.SerializeToString,
            JSRemoteInterface__pb2.SystemWorkMode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def setSensorParam(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/setSensorParam',
            JSRemoteInterface__pb2.SysWorkModeParam.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSensorParam(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/GetSensorParam',
            JSRemoteInterface__pb2.SysWorkModeParam.SerializeToString,
            JSRemoteInterface__pb2.SysWorkModeParam.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def showMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/JSRemoteInterface.JSRemoteControl/showMessage',
            JSRemoteInterface__pb2.TextMessage.SerializeToString,
            JSRemoteInterface__pb2.ResultCode.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
