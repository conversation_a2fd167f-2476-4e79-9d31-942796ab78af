# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: JSRemoteInterface.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'JSRemoteInterface.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17JSRemoteInterface.proto\x12\x11JSRemoteInterface\"*\n\x07\x46loatV3\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\t\n\x01z\x18\x03 \x01(\x02\"5\n\x0cLocation_GEO\x12\x0b\n\x03lat\x18\x01 \x01(\x01\x12\x0b\n\x03lon\x18\x02 \x01(\x01\x12\x0b\n\x03\x61lt\x18\x03 \x01(\x01\"6\n\nFloatV3Rot\x12\x0b\n\x03yaw\x18\x01 \x01(\x02\x12\r\n\x05pitch\x18\x02 \x01(\x02\x12\x0c\n\x04roll\x18\x03 \x01(\x02\"\x0e\n\x0c\x45mptyMessage\"\x1c\n\x08\x45ntityID\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\" \n\x0c\x45ntityIdList\x12\x10\n\x08\x65ntityId\x18\x01 \x03(\t\"\x8c\x01\n\nEntityInfo\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x0f\n\x07\x64istype\x18\x02 \x01(\t\x12\x13\n\x0b\x64isplayName\x18\x03 \x01(\t\x12\x38\n\x0bresouceList\x18\x04 \x01(\x0b\x32#.JSRemoteInterface.ResourceDataList\x12\x0c\n\x04team\x18\x05 \x01(\t\"C\n\x0e\x45ntityInfoList\x12\x31\n\nentityList\x18\x01 \x03(\x0b\x32\x1d.JSRemoteInterface.EntityInfo\"+\n\rSimStateTypes\x12\x1a\n\x12userPermissionCode\x18\x01 \x01(\t\"\x1a\n\nResultCode\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\"e\n\x0cResourceData\x12\x15\n\rcurrentAmount\x18\x01 \x01(\x02\x12\x12\n\nfullAmount\x18\x02 \x01(\x02\x12\x14\n\x0cResourceName\x18\x03 \x01(\t\x12\x14\n\x0cresourceType\x18\x04 \x01(\t\"]\n\x10ResourceDataList\x12\x15\n\rownerEntityId\x18\x01 \x01(\t\x12\x32\n\tresources\x18\x02 \x03(\x0b\x32\x1f.JSRemoteInterface.ResourceData\"\xa6\x03\n\x05Track\x12+\n\x07trackid\x18\x01 \x01(\x0b\x32\x1a.JSRemoteInterface.TrackID\x12\x14\n\x0cTrackQuality\x18\x02 \x01(\x02\x12\x10\n\x08targetId\x18\x03 \x01(\t\x12/\n\x0bNEDvelocity\x18\x04 \x01(\x0b\x32\x1a.JSRemoteInterface.FloatV3\x12\x31\n\x08location\x18\x05 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x32\n\x0borientation\x18\x06 \x01(\x0b\x32\x1d.JSRemoteInterface.FloatV3Rot\x12\x12\n\nentityType\x18\x07 \x01(\t\x12\x15\n\rtargetAzimuth\x18\x08 \x01(\x01\x12\x17\n\x0ftargetElevation\x18\t \x01(\x01\x12\x34\n\x0brefLocation\x18\n \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x1b\n\x13TrackSourceEntityID\x18\x0b \x01(\t\x12\x19\n\x11trackSourceSystem\x18\x0c \x01(\t\":\n\x07TrackID\x12\x15\n\rOwningEntiyID\x18\x01 \x01(\t\x12\x18\n\x10LocalTrackNumber\x18\x02 \x01(\x05\"M\n\tTrackList\x12\x16\n\x0esourceEntityId\x18\x01 \x01(\t\x12(\n\x06tracks\x18\x02 \x03(\x0b\x32\x18.JSRemoteInterface.Track\"\xa0\x01\n\x0bSensorState\x12\x12\n\nsensorName\x18\x01 \x01(\t\x12\x12\n\nsensorType\x18\x02 \x01(\t\x12\x10\n\x08isTurnOn\x18\x03 \x01(\x08\x12\x10\n\x08workmode\x18\x04 \x01(\t\x12\x32\n\x0borientation\x18\x05 \x01(\x0b\x32\x1d.JSRemoteInterface.FloatV3Rot\x12\x11\n\tisDamaged\x18\x06 \x01(\x08\"b\n\x15\x45ntitySensorStateList\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x37\n\x0fsensorStateList\x18\x02 \x03(\x0b\x32\x1e.JSRemoteInterface.SensorState\",\n\x08\x41mmoData\x12\x10\n\x08\x41mmoType\x18\x01 \x01(\t\x12\x0e\n\x06remain\x18\x02 \x01(\x05\"\xc9\x01\n\x0eWeaponResource\x12\x12\n\nweaponName\x18\x01 \x01(\t\x12;\n\nweaponType\x18\x02 \x01(\x0e\x32\'.JSRemoteInterface.WeaponSystemTypeEnum\x12\x33\n\x06status\x18\x03 \x01(\x0e\x32#.JSRemoteInterface.WeaponStatusEnum\x12\x31\n\x0c\x41mmoDataList\x18\x04 \x03(\x0b\x32\x1b.JSRemoteInterface.AmmoData\"Z\n\x12WeaponResourceList\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x32\n\x07weapons\x18\x02 \x03(\x0b\x32!.JSRemoteInterface.WeaponResource\"\xf0\x01\n\x18\x41reaTerrainHeightRequest\x12\x38\n\x0fsouthwestCorner\x18\x01 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x38\n\x0fnortheastCorner\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x13\n\x0blatInterval\x18\x03 \x01(\x02\x12\x13\n\x0blonInterval\x18\x04 \x01(\x02\x12\x1a\n\x12latDegreesInterval\x18\x05 \x01(\x01\x12\x1a\n\x12lonDegreesInterval\x18\x06 \x01(\x01\"\xed\x02\n\x19\x41reaTerrainHeightResponse\x12\x38\n\x0fsouthwestCorner\x18\x01 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x38\n\x0fnortheastCorner\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x1f\n\x17longitudeSampleInterval\x18\x03 \x01(\x01\x12&\n\x1elongitudeSampleDegreesInterval\x18\x04 \x01(\x01\x12\x1d\n\x15longitudeSampleNumber\x18\x05 \x01(\x05\x12\x1e\n\x16latitudeSampleInterval\x18\x06 \x01(\x01\x12%\n\x1dlatitudeSampleDegreesInterval\x18\x07 \x01(\x01\x12\x1c\n\x14latitudeSampleNumber\x18\x08 \x01(\x05\x12\x0f\n\x07heights\x18\t \x03(\x01\"\x1b\n\nEmptyParam\x12\r\n\x05\x65mpty\x18\x01 \x01(\t\"\x1a\n\tPauseParm\x12\r\n\x05pause\x18\x01 \x01(\x08\"\x1c\n\x0cScenarioPath\x12\x0c\n\x04path\x18\x01 \x01(\t\"$\n\x0eTimeMultiplier\x12\x12\n\nmultiplier\x18\x01 \x01(\x02\"\x86\x01\n\x08Waypoint\x12\x0f\n\x07labalId\x18\x01 \x01(\t\x12,\n\x03LLA\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x13\n\x0b\x61rriveSpeed\x18\x03 \x01(\x02\x12\x11\n\tpauseTime\x18\x04 \x01(\x02\x12\x13\n\x0bnextWpLabal\x18\x05 \x01(\t\"G\n\x05Route\x12\x0e\n\x06nameId\x18\x01 \x01(\t\x12.\n\twaypoints\x18\x02 \x03(\x0b\x32\x1b.JSRemoteInterface.Waypoint\"\x83\x01\n\tPhaseLine\x12\x0e\n\x06nameId\x18\x01 \x01(\t\x12\x32\n\tlocation1\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x32\n\tlocation2\x18\x03 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\"Q\n\x0b\x43ontrolArea\x12\x0e\n\x06nameId\x18\x01 \x01(\t\x12\x32\n\tlocations\x18\x02 \x03(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\"\xb8\x01\n\x10\x45ntitySpawnParam\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x12\n\nentityName\x18\x02 \x01(\t\x12\x12\n\nentityType\x18\x03 \x01(\t\x12\x11\n\tforceType\x18\x04 \x01(\t\x12\x31\n\x08location\x18\x05 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x0f\n\x07heading\x18\x06 \x01(\x02\x12\x13\n\x0b\x63ommanderID\x18\x07 \x01(\t\"N\n\rRouteTaskInfo\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12+\n\trouteInfo\x18\x02 \x01(\x0b\x32\x18.JSRemoteInterface.Route\"Y\n\x12moveToLocationTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x31\n\x08location\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\"b\n\x10\x46ollowEntityTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x10\n\x08leaderId\x18\x02 \x01(\t\x12*\n\x06offset\x18\x03 \x01(\x0b\x32\x1a.JSRemoteInterface.FloatV3\"\x93\x01\n\x19\x46lyHeadingAndAltitudeTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x0f\n\x07heading\x18\x02 \x01(\x02\x12\x10\n\x08turnRate\x18\x03 \x01(\x02\x12\x10\n\x08\x61ltitude\x18\x04 \x01(\x02\x12\x18\n\x10\x63limbDescentRate\x18\x05 \x01(\x02\x12\x15\n\rturnDirection\x18\x06 \x01(\x11\"\x88\x01\n\tOrbitTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12/\n\x06\x63\x65nter\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x15\n\rentityToOrbit\x18\x03 \x01(\t\x12\x0e\n\x06radius\x18\x04 \x01(\x02\x12\x11\n\tclockwise\x18\x05 \x01(\x08\"+\n\x08SpeedSet\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\r\n\x05speed\x18\x02 \x01(\x02\"K\n\nTargetTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12+\n\x07trackId\x18\x02 \x01(\x0b\x32\x1a.JSRemoteInterface.TrackID\"\x95\x01\n\x10\x46ireAtTargetTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x14\n\x0cweaponToFire\x18\x02 \x01(\t\x12/\n\x0btargettrack\x18\x03 \x01(\x0b\x32\x1a.JSRemoteInterface.TrackID\x12\x17\n\x0fmaxRoundsToFire\x18\x04 \x01(\x05\x12\x0f\n\x07routeID\x18\x05 \x01(\t\"P\n\x0bTargetPoint\x12\x0e\n\x06nameId\x18\x01 \x01(\t\x12\x31\n\x08location\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\"4\n\x0fJammerEnableSet\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x0f\n\x07\x65nanble\x18\x02 \x01(\x08\"T\n\rLasePointTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x31\n\x08location\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\"-\n\x0bLaseCodeSet\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\x05\"\x88\x01\n\x12\x46ireAtLocationTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x31\n\x08location\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x14\n\x0cweaponToFire\x18\x03 \x01(\t\x12\x17\n\x0fmaxRoundsToFire\x18\x04 \x01(\x05\"H\n\x0fSystemEnableSet\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x12\n\nsystemName\x18\x02 \x01(\t\x12\x0f\n\x07\x65nanble\x18\x03 \x01(\x08\"\x93\x01\n\x11MoverSpeedCtrlSet\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x36\n\x12targetLineSpeedNED\x18\x02 \x01(\x0b\x32\x1a.JSRemoteInterface.FloatV3\x12\x34\n\x10targetAngleSpeed\x18\x03 \x01(\x0b\x32\x1a.JSRemoteInterface.FloatV3\"q\n\x12SystemRotationTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x12\n\nsystemName\x18\x02 \x01(\t\x12\x35\n\x0etargetRotation\x18\x03 \x01(\x0b\x32\x1d.JSRemoteInterface.FloatV3Rot\"T\n\x0eJammingTargets\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x30\n\x0ctargetTracks\x18\x02 \x03(\x0b\x32\x1a.JSRemoteInterface.TrackID\"^\n\x0eLaunchResource\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x14\n\x0cresourcename\x18\x02 \x01(\t\x12\x0e\n\x06number\x18\x03 \x01(\x05\x12\x14\n\x0ctimeInterval\x18\x04 \x01(\x01\"7\n\x15\x45ntityTypeDescription\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x65sc\x18\x02 \x01(\t\"N\n\x0eSystemWorkMode\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x14\n\x0csystemNameId\x18\x02 \x01(\t\x12\x14\n\x0cWorkModeName\x18\x03 \x01(\t\"{\n\x0fTrackTargetTask\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x14\n\x0csystemNameId\x18\x02 \x01(\t\x12+\n\x07trackId\x18\x03 \x01(\x0b\x32\x1a.JSRemoteInterface.TrackID\x12\x13\n\x0bStartorStop\x18\x04 \x01(\x08\"a\n\x10SysWorkModeParam\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x14\n\x0cSystemNameId\x18\x02 \x01(\t\x12\x11\n\tparamName\x18\x03 \x01(\t\x12\x12\n\nparamValue\x18\x04 \x01(\t\":\n\x10TrackTargetQuery\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x14\n\x0csystemNameId\x18\x02 \x01(\t\"C\n\x0fTrackTargetList\x12\x30\n\x0ctrackTargets\x18\x01 \x03(\x0b\x32\x1a.JSRemoteInterface.TrackID\"g\n\x0cLaserIrrCtrl\x12\x13\n\x0brangingFreq\x18\x01 \x01(\x01\x12\x14\n\x0claserIrrCode\x18\x02 \x01(\r\x12\x15\n\rlaserIrrDelay\x18\x03 \x01(\r\x12\x15\n\rlaserIrrCycle\x18\x04 \x01(\r\"\xd3\x01\n\x07\x45WEvent\x12\x0e\n\x06lotNum\x18\x01 \x01(\r\x12\r\n\x05state\x18\x02 \x01(\r\x12\x10\n\x08platType\x18\x03 \x01(\r\x12\x0b\n\x03iff\x18\x04 \x01(\r\x12\x11\n\tplatModel\x18\x05 \x01(\t\x12\x0b\n\x03\x61zi\x18\x06 \x01(\x01\x12\x0b\n\x03\x65le\x18\x07 \x01(\x01\x12\x16\n\x0eisRadarWarning\x18\x08 \x01(\x08\x12\x18\n\x10isMissileWarning\x18\t \x01(\x08\x12\x16\n\x0eisLaserWarning\x18\n \x01(\x08\x12\x13\n\x0bthreatLevel\x18\x0b \x01(\x01\"\xdf\x02\n\x0b\x45ntityState\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x11\n\tforceType\x18\x02 \x01(\t\x12\x12\n\nentityType\x18\x03 \x01(\t\x12\r\n\x05speed\x18\x04 \x01(\x02\x12,\n\x08velocity\x18\x05 \x01(\x0b\x32\x1a.JSRemoteInterface.FloatV3\x12\x31\n\x08location\x18\x06 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x32\n\x0borientation\x18\x07 \x01(\x0b\x32\x1d.JSRemoteInterface.FloatV3Rot\x12\x13\n\x0b\x64isplayname\x18\x08 \x01(\t\x12\x0e\n\x06\x64\x61mage\x18\t \x01(\x02\x12\x0b\n\x03\x61gl\x18\n \x01(\x02\x12\x12\n\nisEmbarked\x18\x0b \x01(\x08\x12\x1a\n\x12\x65mbarkedOnEntityId\x18\x0c \x01(\t\x12\x11\n\tcommander\x18\r \x01(\t\"G\n\x0f\x45ntityStateList\x12\x34\n\x0c\x65ntityStates\x18\x01 \x03(\x0b\x32\x1e.JSRemoteInterface.EntityState\"\xf8\x03\n\x08SimState\x12\x35\n\x0bsimRunState\x18\x01 \x01(\x0b\x32\x1e.JSRemoteInterface.SimRunStateH\x00\x12:\n\x0c\x65ntityStates\x18\x02 \x01(\x0b\x32\".JSRemoteInterface.EntityStateListH\x00\x12\x36\n\x05ldEvt\x18\x03 \x01(\x0b\x32%.JSRemoteInterface.LaserDesignatorEvtH\x00\x12/\n\x07\x66ireEvt\x18\x04 \x01(\x0b\x32\x1c.JSRemoteInterface.FireEventH\x00\x12;\n\rdetonationEvt\x18\x05 \x01(\x0b\x32\".JSRemoteInterface.DetonationEventH\x00\x12\x31\n\x06MsgEvt\x18\x06 \x01(\x0b\x32\x1f.JSRemoteInterface.MessageEventH\x00\x12\x36\n\tentityOpt\x18\x08 \x01(\x0b\x32!.JSRemoteInterface.EntityOptEventH\x00\x12+\n\x05\x65wEvt\x18\t \x01(\x0b\x32\x1a.JSRemoteInterface.EWEventH\x00\x12\x32\n\x08SenorEvt\x18\n \x01(\x0b\x32\x1e.JSRemoteInterface.SensorEventH\x00\x42\x07\n\x05state\"\xfc\x01\n\x0bWeatherInfo\x12\x37\n\x0bweatherType\x18\x01 \x01(\x0e\x32\".JSRemoteInterface.WeatherTypeEnum\x12\x0c\n\x04rain\x18\x02 \x01(\x02\x12\x0c\n\x04snow\x18\x03 \x01(\x02\x12\x0b\n\x03\x66og\x18\x04 \x01(\x02\x12\x0c\n\x04\x64ust\x18\x05 \x01(\x02\x12\r\n\x05\x63loud\x18\x06 \x01(\x02\x12\x12\n\nvisibility\x18\x07 \x01(\x02\x12\x11\n\tfogHeight\x18\x08 \x01(\x02\x12\x11\n\twindSpeed\x18\t \x01(\x02\x12\x15\n\rwindDirection\x18\n \x01(\x02\x12\x1d\n\x15\x61mbientAirTemperature\x18\x0b \x01(\x02\"\xa4\x01\n\x07SeaInfo\x12\x13\n\x0btidalOffset\x18\x01 \x01(\x02\x12\x16\n\x0etidalDirection\x18\x02 \x01(\x02\x12\x12\n\nswellState\x18\x03 \x01(\x02\x12\x1c\n\x14underwaterVisibility\x18\x04 \x01(\x02\x12\x19\n\x11waterCurrentSpeed\x18\x05 \x01(\x02\x12\x1f\n\x17\x61mbientWaterTemperature\x18\x06 \x01(\x02\"\xfb\x01\n\x0bSimRunState\x12\x37\n\x0bruningState\x18\x01 \x01(\x0e\x32\".JSRemoteInterface.SimRunStateEnum\x12\x16\n\x0etimeMultiplier\x18\x02 \x01(\x02\x12\x0f\n\x07simtime\x18\x03 \x01(\x01\x12\x30\n\x0bsimDateTime\x18\x04 \x01(\x0b\x32\x1b.JSRemoteInterface.dateTime\x12/\n\x07weather\x18\x05 \x01(\x0b\x32\x1e.JSRemoteInterface.WeatherInfo\x12\'\n\x03sea\x18\x06 \x01(\x0b\x32\x1a.JSRemoteInterface.SeaInfo\"b\n\x08\x64\x61teTime\x12\x0c\n\x04year\x18\x01 \x01(\x05\x12\r\n\x05month\x18\x02 \x01(\x05\x12\x0b\n\x03\x64\x61y\x18\x03 \x01(\x05\x12\x0c\n\x04hour\x18\x04 \x01(\x05\x12\x0e\n\x06minute\x18\x05 \x01(\x05\x12\x0e\n\x06second\x18\x06 \x01(\x05\"\xbb\x02\n\x14\x41rticulatedPartState\x12\x34\n\x08partType\x18\x01 \x01(\x0e\x32\".JSRemoteInterface.ArtPartTypeEnum\x12\x1b\n\x13\x61ttachedToPartIndex\x18\x02 \x01(\r\x12/\n\x0btranslation\x18\x03 \x01(\x0b\x32\x1a.JSRemoteInterface.FloatV3\x12\x33\n\x0ftranslationRate\x18\x04 \x01(\x0b\x32\x1a.JSRemoteInterface.FloatV3\x12\x32\n\x0borientation\x18\x05 \x01(\x0b\x32\x1d.JSRemoteInterface.FloatV3Rot\x12\x36\n\x0forientationRate\x18\x06 \x01(\x0b\x32\x1d.JSRemoteInterface.FloatV3Rot\"\xd1\x01\n\x12LaserDesignatorEvt\x12\x16\n\x0esourceEntityId\x18\x01 \x01(\t\x12\x1a\n\x12\x64\x65signatedEntityId\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\r\x12\r\n\x05power\x18\x04 \x01(\x02\x12\x12\n\nwavelength\x18\x05 \x01(\x02\x12\x31\n\x08location\x18\x06 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x12\n\npowerState\x18\x07 \x01(\x08\x12\x0f\n\x07simTime\x18\x08 \x01(\x01\"\x93\x03\n\x10\x45mitterBeamState\x12\x15\n\razimuthCenter\x18\x01 \x01(\x02\x12\x14\n\x0c\x61zimuthSweep\x18\x02 \x01(\x02\x12\x17\n\x0f\x65levationCenter\x18\x03 \x01(\x02\x12\x16\n\x0e\x65levationSweep\x18\x04 \x01(\x02\x12\x35\n\x0c\x62\x65\x61mFunction\x18\x05 \x01(\x0e\x32\x1f.JSRemoteInterface.BeamFuncEnum\x12\x12\n\nbeamStatus\x18\x06 \x01(\r\x12\x0e\n\x06\x62\x65\x61mId\x18\x07 \x01(\r\x12\x0b\n\x03\x45RP\x18\x08 \x01(\x02\x12\x11\n\tfrequency\x18\t \x01(\x02\x12\x16\n\x0e\x66requencyRange\x18\n \x01(\x02\x12\x0b\n\x03PRF\x18\x0b \x01(\x02\x12\x12\n\npulseWidth\x18\x0c \x01(\x02\x12\x11\n\tsweepSync\x18\r \x01(\x02\x12\x13\n\x0bhighDensity\x18\x0e \x01(\x08\x12\x17\n\x0ftrackJamTargets\x18\x0f \x03(\t\x12\x0f\n\x07jamBeam\x18\x10 \x01(\x08\x12\x1b\n\x13jammingModeSequence\x18\x11 \x01(\r\"\x9d\x03\n\tFireEvent\x12\x12\n\nattackerId\x18\x01 \x01(\t\x12/\n\x0btargetTrack\x18\x02 \x01(\x0b\x32\x1a.JSRemoteInterface.TrackID\x12\x37\n\x0etargetlocation\x18\x03 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x12\n\nmunitionId\x18\x04 \x01(\t\x12\x0f\n\x07\x65ventId\x18\x05 \x01(\t\x12\x35\n\x0c\x46irelocation\x18\x06 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x14\n\x0cmunitionType\x18\x07 \x01(\t\x12\x37\n\x0bwarheadType\x18\x08 \x01(\x0e\x32\".JSRemoteInterface.WarheadTypeEnum\x12\x36\n\x08\x66useType\x18\t \x01(\x0e\x32$.JSRemoteInterface.DetonatorFuzeEnum\x12\x10\n\x08quantity\x18\n \x01(\r\x12\x0c\n\x04rate\x18\x0b \x01(\r\x12\x0f\n\x07simTime\x18\x0c \x01(\x01\"\xee\x01\n\x0e\x66\x66\x65TargetParam\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x31\n\rtargetTrackID\x18\x02 \x01(\x0b\x32\x1a.JSRemoteInterface.TrackID\x12\x35\n\x0c\x46irelocation\x18\x03 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x12\n\nweaponName\x18\x04 \x01(\t\x12\x16\n\x0enumberOfRounds\x18\x05 \x01(\r\x12\x18\n\x10\x64ispersionRadius\x18\x06 \x01(\x02\x12\x1a\n\x12heightAboveTerrain\x18\x07 \x01(\x02\"\xf5\x01\n\x0f\x44\x65tonationEvent\x12\x12\n\nattackerId\x18\x01 \x01(\t\x12\x10\n\x08targetId\x18\x02 \x01(\t\x12\x12\n\nmunitionId\x18\x03 \x01(\t\x12\x0f\n\x07\x65ventId\x18\x04 \x01(\t\x12\x37\n\x0eimpactLocation\x18\x05 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12\x37\n\x06result\x18\x06 \x01(\x0e\x32\'.JSRemoteInterface.DetonationResultEnum\x12\x14\n\x0cmunitionType\x18\x07 \x01(\t\x12\x0f\n\x07simTime\x18\x08 \x01(\x01\"\xa1\x01\n\x10MessageSendEvent\x12\x10\n\x08SenderId\x18\x01 \x01(\t\x12\x17\n\x0fSenderComSystem\x18\x02 \x01(\t\x12\x13\n\x0bmessagetype\x18\x03 \x01(\t\x12\x12\n\nReceiverId\x18\x04 \x01(\t\x12\x19\n\x11ReceiverComSystem\x18\x05 \x01(\t\x12\r\n\x05\x65vtID\x18\x06 \x01(\t\x12\x0f\n\x07simTime\x18\x07 \x01(\x01\"r\n\x10MessageRecvEvent\x12\x12\n\nReceiverId\x18\x01 \x01(\t\x12\x15\n\rcomSystemName\x18\x02 \x01(\t\x12\x13\n\x0bmessagetype\x18\x03 \x01(\t\x12\r\n\x05\x65vtID\x18\x04 \x01(\t\x12\x0f\n\x07simTime\x18\x05 \x01(\x01\"\x95\x01\n\x0cMessageEvent\x12=\n\x0emessageSendEvt\x18\x01 \x01(\x0b\x32#.JSRemoteInterface.MessageSendEventH\x00\x12=\n\x0emessageRecvEvt\x18\x02 \x01(\x0b\x32#.JSRemoteInterface.MessageRecvEventH\x00\x42\x07\n\x05state\"g\n\x0e\x45ntityOptEvent\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\t\x12\x10\n\x08IsAddEvt\x18\x02 \x01(\x08\x12\x11\n\tentiyType\x18\x03 \x01(\t\x12\r\n\x05\x65vtID\x18\x04 \x01(\t\x12\x0f\n\x07simTime\x18\x05 \x01(\x01\"#\n\x0b\x44\x61mageState\x12\x14\n\x0c\x64\x61magefactor\x18\x01 \x01(\x02\"\x1e\n\x0bTextMessage\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\"?\n\nSensorList\x12\x31\n\nsensorInfo\x18\x01 \x03(\x0b\x32\x1d.JSRemoteInterface.SensorInfo\"*\n\nSensorInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06\x65nable\x18\x02 \x01(\x08\"j\n\x06Points\x12/\n\x06point1\x18\x01 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\x12/\n\x06point2\x18\x02 \x01(\x0b\x32\x1f.JSRemoteInterface.Location_GEO\" \n\nVisibility\x12\x12\n\nvisibility\x18\x01 \x01(\x08\"\x95\x01\n\x0bSensorEvent\x12\x10\n\x08\x65ntityID\x18\x01 \x01(\t\x12\x12\n\nsensorName\x18\x02 \x01(\t\x12\x13\n\x0b\x46oundTarget\x18\x03 \x01(\x08\x12+\n\x07trackId\x18\x04 \x01(\x0b\x32\x1a.JSRemoteInterface.TrackID\x12\r\n\x05\x65vtID\x18\x05 \x01(\t\x12\x0f\n\x07simTime\x18\x06 \x01(\x01*\xac\x01\n\x10WeaponStatusEnum\x12\x0e\n\nOK_TO_FIRE\x10\x00\x12\x0c\n\x08\x44ISABLED\x10\x01\x12\x1c\n\x18NO_AMMUNITION_FOR_TARGET\x10\x02\x12\x19\n\x15\x43\x41NNOT_ACQUIRE_TARGET\x10\x04\x12\x0e\n\nSUPPRESSED\x10\x08\x12 \n\x1cOUT_OF_AMMUNITION_FOR_WEAPON\x10\x10\x12\x0f\n\x0bWEAPON_BUSY\x10 *`\n\x11SensorAimModeEnum\x12\x0f\n\x0b\x46IXED_ANGLE\x10\x00\x12\x10\n\x0cTRACK_ENTITY\x10\x01\x12\x12\n\x0eTRACK_LOCATION\x10\x02\x12\x08\n\x04SCAN\x10\x03\x12\n\n\x06MANUAL\x10\x04*\x90\x01\n\x10\x44\x65\x66\x65nseDirection\x12\x12\n\x0e\x41UTO_DIRECTION\x10\x00\x12\t\n\x05\x46RONT\x10\x01\x12\x0e\n\nLEFT_FRONT\x10\x02\x12\x08\n\x04LEFT\x10\x03\x12\r\n\tLEFT_BACK\x10\x04\x12\x08\n\x04\x42\x41\x43K\x10\x05\x12\x0e\n\nRIGHT_BACK\x10\x06\x12\t\n\x05RIGHT\x10\x07\x12\x0f\n\x0bRIGHT_FRONT\x10\x08*\xea\x01\n\x0fWeatherTypeEnum\x12\r\n\tLIGHTRAIN\x10\x00\x12\x08\n\x04RAIN\x10\x01\x12\r\n\tHEAVYRAIN\x10\x02\x12\x10\n\x0cTHUNDERSTORM\x10\x03\x12\r\n\tLIGHTSNOW\x10\x04\x12\x08\n\x04SNOW\x10\x05\x12\r\n\tHEAVYSNOW\x10\x06\x12\x0b\n\x07\x42IZZARD\x10\x07\x12\r\n\tDUSTSTORM\x10\x08\x12\x08\n\x04\x44UST\x10\t\x12\t\n\x05\x46OGGY\x10\n\x12\x0c\n\x08\x43LEARSKY\x10\x0b\x12\x10\n\x0cPARTLYCLOUDY\x10\x0c\x12\n\n\x06\x43LOUDY\x10\r\x12\x0c\n\x08OVERCAST\x10\x0e\x12\n\n\x06\x43USTOM\x10\x0f*\x8c\x01\n\x0fSimRunStateEnum\x12\x16\n\x12PENDING_INITIALIZE\x10\x00\x12\x10\n\x0cINITIALIZING\x10\x01\x12\x11\n\rPENDING_START\x10\x02\x12\x0c\n\x08STARTING\x10\x03\x12\n\n\x06\x41\x43TIVE\x10\x04\x12\x14\n\x10PENDING_COMPLETE\x10\x05\x12\x0c\n\x08\x43OMPLETE\x10\x06*\x97\t\n\x0f\x41rtPartTypeEnum\x12\x10\n\x0c\x44tEntityBase\x10\x00\x12\r\n\x08\x44tRudder\x10\x80\x08\x12\x0f\n\nDtLeftFlap\x10\xa0\x08\x12\x10\n\x0b\x44tRightFlap\x10\xc0\x08\x12\x12\n\rDtLeftAileron\x10\xe0\x08\x12\x13\n\x0e\x44tRightAileron\x10\x80\t\x12\x1a\n\x15\x44tHelicopterMainRotor\x10\xa0\t\x12\x1a\n\x15\x44tHelicopterTailRotor\x10\xc0\t\x12\x1b\n\x16\x44tOtherAircraftControl\x10\xe0\t\x12\x10\n\x0b\x44tPeriscope\x10\x80\x10\x12\x15\n\x10\x44tGenericAntenna\x10\xa0\x10\x12\x0e\n\tDtSnorkel\x10\xc0\x10\x12\x19\n\x14\x44tOtherSubExtendable\x10\xe0\x10\x12\x12\n\rDtLandingGear\x10\x80\x18\x12\x0f\n\nDtTailHook\x10\xa0\x18\x12\x11\n\x0c\x44tSpeedBrake\x10\xc0\x18\x12\x18\n\x13\x44tLeftWeaponBayDoor\x10\xe0\x18\x12\x19\n\x14\x44tRightWeaponBayDoor\x10\x80\x19\x12\x13\n\x0e\x44tTankAPCHatch\x10\xa0\x19\x12\x10\n\x0b\x44tWingSweep\x10\xc0\x19\x12\x15\n\x10\x44tPrimaryTurret1\x10\x80 \x12\x15\n\x10\x44tPrimaryTurret2\x10\xa0 \x12\x15\n\x10\x44tPrimaryTurret3\x10\xc0 \x12\x12\n\rDtPrimaryGun1\x10\xc0\"\x12\x12\n\rDtPrimaryGun2\x10\xe0\"\x12\x12\n\rDtPrimaryGun3\x10\x80#\x12\x17\n\x12\x44tPrimaryLauncher1\x10\x80%\x12\x17\n\x12\x44tPrimaryLauncher2\x10\xa0%\x12\x17\n\x12\x44tPrimaryLauncher3\x10\xc0%\x12\x19\n\x14\x44tPrimaryDefenseSys1\x10\xc0\'\x12\x19\n\x14\x44tPrimaryDefenseSys2\x10\xe0\'\x12\x19\n\x14\x44tPrimaryDefenseSys3\x10\x80(\x12\x14\n\x0f\x44tPrimaryRadar1\x10\x80*\x12\x14\n\x0f\x44tPrimaryRadar2\x10\xa0*\x12\x14\n\x0f\x44tPrimaryRadar3\x10\xc0*\x12\x17\n\x12\x44tSecondaryTurret1\x10\xc0,\x12\x17\n\x12\x44tSecondaryTurret2\x10\xe0,\x12\x17\n\x12\x44tSecondaryTurret3\x10\x80-\x12\x14\n\x0f\x44tSecondaryGun1\x10\x80/\x12\x14\n\x0f\x44tSecondaryGun2\x10\xa0/\x12\x14\n\x0f\x44tSecondaryGun3\x10\xc0/\x12\x19\n\x14\x44tSecondaryLauncher1\x10\xc0\x31\x12\x19\n\x14\x44tSecondaryLauncher2\x10\xe0\x31\x12\x19\n\x14\x44tSecondaryLauncher3\x10\x80\x32\x12\x1b\n\x16\x44tSecondaryDefenseSys1\x10\x80\x34\x12\x1b\n\x16\x44tSecondaryDefenseSys2\x10\xa0\x34\x12\x1b\n\x16\x44tSecondaryDefenseSys3\x10\xc0\x34\x12\x16\n\x11\x44tSecondaryRadar1\x10\xc0\x36\x12\x16\n\x11\x44tSecondaryRadar2\x10\xe0\x36\x12\x16\n\x11\x44tSecondaryRadar3\x10\x80\x37*\xf7\x01\n\x12\x45ntityKindTypeEnum\x12\x12\n\x0e\x45KT_OTHER_KIND\x10\x00\x12\x15\n\x11\x45KT_PLATFORM_KIND\x10\x01\x12\x15\n\x11\x45KT_MUNITION_KIND\x10\x02\x12\x15\n\x11\x45KT_LIFEFORM_KIND\x10\x03\x12\x1a\n\x16\x45KT_ENVIRONMENTAL_KIND\x10\x04\x12\x15\n\x11\x45KT_CULTURAL_KIND\x10\x05\x12\x13\n\x0f\x45KT_SUPPLY_KIND\x10\x06\x12\x12\n\x0e\x45KT_RADIO_KIND\x10\x07\x12\x17\n\x13\x45KT_EXPENDABLE_KIND\x10\x08\x12\x13\n\x0f\x45KT_SENSOR_KIND\x10\t*\xa4\x01\n\x14WeaponSystemTypeEnum\x12\x12\n\x0eWEAPON_ALLTYPE\x10\x00\x12\x1f\n\x1bWEAPON_DIRECTFIRE_BALLISTIC\x10\x01\x12\x1e\n\x1aWEAPON_DIRECTFIRE_LAUNCHER\x10\x02\x12\x1d\n\x19WEAPON_INDIRECT_ARTILLERY\x10\x03\x12\x18\n\x14WEAPON_INDIRECT_BOMB\x10\x04*\xb0\x02\n\x14MunitionKindTypeEnum\x12\r\n\tMKT_OTHER\x10\x00\x12\x0f\n\x0bMKT_ANTIAIR\x10\x01\x12\x11\n\rMKT_ANTIARMOR\x10\x02\x12\x19\n\x15MKT_ANTIGUIDED_WEAPON\x10\x03\x12\x11\n\rMKT_ANTIRADAR\x10\x04\x12\x15\n\x11MKT_ANTISATELLITE\x10\x05\x12\x10\n\x0cMKT_ANTISHIP\x10\x06\x12\x15\n\x11MKT_ANTISUBMARINE\x10\x07\x12\x15\n\x11MKT_ANTIPERSONNEL\x10\x08\x12\x1b\n\x17MKT_BATTLEFIELD_SUPPORT\x10\t\x12\x11\n\rMKT_STRATEGIC\x10\n\x12\x10\n\x0cMKT_TACTICAL\x10\x0b\x12\x1e\n\x1aMKT_DIRECTED_ENERGY_WEAPON\x10\x0c*\xcb\x02\n\x11LifeformStateEnum\x12\x0f\n\x0bLIFEFORM_NA\x10\x00\x12\x15\n\x11LIFEFORM_STANDING\x10\x01\x12\x14\n\x10LIFEFORM_WALKING\x10\x02\x12\x14\n\x10LIFEFORM_RUNNING\x10\x03\x12\x15\n\x11LIFEFORM_KNEELING\x10\x04\x12\x12\n\x0eLIFEFORM_PRONE\x10\x05\x12\x15\n\x11LIFEFORM_CRAWLING\x10\x06\x12\x15\n\x11LIFEFORM_SWIMMING\x10\x07\x12\x18\n\x14LIFEFORM_PARACHUTING\x10\x08\x12\x14\n\x10LIFEFORM_JUMPING\x10\t\x12\x14\n\x10LIFEFORM_SITTING\x10\n\x12\x16\n\x12LIFEFORM_SQUATTING\x10\x0b\x12\x16\n\x12LIFEFORM_CROUCHING\x10\x0c\x12\x13\n\x0fLIFEFORM_WADING\x10\r*\xe0\x12\n\x10\x45missionFuncEnum\x12\x17\n\x13\x45MISSION_FUN_cOther\x10\x00\x12\x1f\n\x1b\x45MISSION_FUN_cMultiFunction\x10\x01\x12*\n&EMISSION_FUN_cEarlyWarningSurveillance\x10\x02\x12\x1f\n\x1b\x45MISSION_FUN_cHeightFinding\x10\x03\x12\x1d\n\x19\x45MISSION_FUN_cFireControl\x10\x04\x12&\n\"EMISSION_FUN_cAcquisitionDetection\x10\x05\x12\x1a\n\x16\x45MISSION_FUN_cTracking\x10\x06\x12&\n\"EMISSION_FUN_cGuidanceIllumination\x10\x07\x12\x30\n,EMISSION_FUN_cFiringPointLaunchPointLocation\x10\x08\x12\x19\n\x15\x45MISSION_FUN_cRanging\x10\t\x12 \n\x1c\x45MISSION_FUN_cRadarAltimeter\x10\n\x12\x19\n\x15\x45MISSION_FUN_cImaging\x10\x0b\x12!\n\x1d\x45MISSION_FUN_cMotionDetection\x10\x0c\x12\x1c\n\x18\x45MISSION_FUN_cNavigation\x10\r\x12\x19\n\x15\x45MISSION_FUN_cWeather\x10\x0e\x12!\n\x1d\x45MISSION_FUN_cInstrumentation\x10\x0f\x12.\n*EMISSION_FUN_cIdentificationClassification\x10\x10\x12:\n6EMISSION_FUN_cAAA_Anti_Aircraft_Artillery_Fire_Control\x10\x11\x12!\n\x1d\x45MISSION_FUN_cAir_Search_Bomb\x10\x12\x12\x1f\n\x1b\x45MISSION_FUN_cAir_Intercept\x10\x13\x12\x1b\n\x17\x45MISSION_FUN_cAltimeter\x10\x14\x12\x1d\n\x19\x45MISSION_FUN_cAir_Mapping\x10\x15\x12%\n!EMISSION_FUN_cAir_Traffic_Control\x10\x16\x12\x18\n\x14\x45MISSION_FUN_cBeacon\x10\x17\x12*\n&EMISSION_FUN_cBattlefield_Surveillance\x10\x18\x12)\n%EMISSION_FUN_cGround_Control_Approach\x10\x19\x12*\n&EMISSION_FUN_cGround_Control_Intercept\x10\x1a\x12&\n\"EMISSION_FUN_cCoastal_Surveillance\x10\x1b\x12\x1d\n\x19\x45MISSION_FUN_cDecoy_Mimic\x10\x1c\x12#\n\x1f\x45MISSION_FUN_cData_Transmission\x10\x1d\x12$\n EMISSION_FUN_cEarth_Surveillance\x10\x1e\x12 \n\x1c\x45MISSION_FUN_cGun_Lay_Beacon\x10\x1f\x12 \n\x1c\x45MISSION_FUN_cGround_Mapping\x10 \x12%\n!EMISSION_FUN_cHarbor_Surveillance\x10!\x12/\n+EMISSION_FUN_cILS_Instrument_Landing_System\x10#\x12#\n\x1f\x45MISSION_FUN_cIonospheric_Sound\x10$\x12\x1e\n\x1a\x45MISSION_FUN_cInterrogator\x10%\x12!\n\x1d\x45MISSION_FUN_cBarrage_Jamming\x10&\x12\x1f\n\x1b\x45MISSION_FUN_cClick_Jamming\x10\'\x12)\n%EMISSION_FUN_cFrequency_Swept_Jamming\x10)\x12\x19\n\x15\x45MISSION_FUN_cJamming\x10*\x12 \n\x1c\x45MISSION_FUN_cPulsed_Jamming\x10,\x12\"\n\x1e\x45MISSION_FUN_cRepeater_Jamming\x10-\x12$\n EMISSION_FUN_cSpot_Noise_Jamming\x10.\x12%\n!EMISSION_FUN_cMissile_Acquisition\x10/\x12\"\n\x1e\x45MISSION_FUN_cMissile_Downlink\x10\x30\x12\x17\n\x13\x45MISSION_FUN_cSpace\x10\x32\x12 \n\x1c\x45MISSION_FUN_cSurface_Search\x10\x33\x12 \n\x1c\x45MISSION_FUN_cShell_Tracking\x10\x34\x12\x1c\n\x18\x45MISSION_FUN_cTelevision\x10\x38\x12\x19\n\x15\x45MISSION_FUN_cUnknown\x10\x39\x12 \n\x1c\x45MISSION_FUN_cVideo_Remoting\x10:\x12*\n&EMISSION_FUN_cExperimental_or_Training\x10;\x12\"\n\x1e\x45MISSION_FUN_cMissile_Guidance\x10<\x12 \n\x1c\x45MISSION_FUN_cMissile_Homing\x10=\x12\"\n\x1e\x45MISSION_FUN_cMissile_Tracking\x10>\x12\x1e\n\x1a\x45MISSION_FUN_cJammingNoise\x10@\x12\"\n\x1e\x45MISSION_FUN_cJammingDeception\x10\x41\x12\x17\n\x13\x45MISSION_FUN_cDecoy\x10\x42\x12\x39\n5EMISSION_FUN_cNavigation_Distance_Measuring_Equipment\x10G\x12#\n\x1f\x45MISSION_FUN_cTerrain_Following\x10H\x12#\n\x1f\x45MISSION_FUN_cWeather_Avoidance\x10I\x12 \n\x1c\x45MISSION_FUN_cProximity_Fuse\x10J\x12\x1c\n\x18\x45MISSION_FUN_cRadiosonde\x10L\x12\x1a\n\x16\x45MISSION_FUN_cSonobuoy\x10M\x12!\n\x1d\x45MISSION_FUN_cWeaponNonLethal\x10`\x12\x1e\n\x1a\x45MISSION_FUN_cWeaponLethal\x10\x61*\xfa\x03\n\x0c\x42\x65\x61mFuncEnum\x12\x12\n\x0e\x42\x45\x41MFUNC_Other\x10\x00\x12\x13\n\x0f\x42\x45\x41MFUNC_Search\x10\x01\x12\x19\n\x15\x42\x45\x41MFUNC_HeightFinder\x10\x02\x12\x18\n\x14\x42\x45\x41MFUNC_Acquisition\x10\x03\x12\x15\n\x11\x42\x45\x41MFUNC_Tracking\x10\x04\x12#\n\x1f\x42\x45\x41MFUNC_AcquisitionAndTracking\x10\x05\x12\x1c\n\x18\x42\x45\x41MFUNC_CommandGuidance\x10\x06\x12\x19\n\x15\x42\x45\x41MFUNC_Illumination\x10\x07\x12\x1b\n\x17\x42\x45\x41MFUNC_RangeOnlyRadar\x10\x08\x12\x1a\n\x16\x42\x45\x41MFUNC_MissileBeacon\x10\t\x12\x18\n\x14\x42\x45\x41MFUNC_MissileFuze\x10\n\x12%\n!BEAMFUNC_ActiveRadarMissileSeeker\x10\x0b\x12\x13\n\x0f\x42\x45\x41MFUNC_Jammer\x10\x0c\x12\x10\n\x0c\x42\x45\x41MFUNC_IFF\x10\r\x12\x17\n\x13\x42\x45\x41MFUNC_NavWeather\x10\x0e\x12\x1b\n\x17\x42\x45\x41MFUNC_Meteorological\x10\x0f\x12\x1d\n\x19\x42\x45\x41MFUNC_DataTransmission\x10\x10\x12!\n\x1d\x42\x45\x41MFUNC_NavDirectionalBeacon\x10\x11*\xcc\x04\n\x0fWarheadTypeEnum\x12\x11\n\rWARHEAD_Other\x10\x00\x12\x1a\n\x15WARHEAD_HighExplosive\x10\xe8\x07\x12!\n\x1cWARHEAD_HighExplosivePlastic\x10\xcc\x08\x12$\n\x1fWARHEAD_HighExplosiveIncendiary\x10\xb0\t\x12\'\n\"WARHEAD_HighExplosiveFragmentation\x10\x94\n\x12\"\n\x1dWARHEAD_HighExplosiveAntiTank\x10\xf8\n\x12\"\n\x1dWARHEAD_HighExplosiveBomblets\x10\xdc\x0b\x12&\n!WARHEAD_HighExplosiveShapedCharge\x10\xc0\x0c\x12\x12\n\rWARHEAD_Smoke\x10\xd0\x0f\x12\x19\n\x14WARHEAD_Illumination\x10\xb8\x17\x12\x15\n\x10WARHEAD_Practice\x10\xa0\x1f\x12\x14\n\x0fWARHEAD_Kinetic\x10\x88\'\x12\x13\n\x0eWARHEAD_Unused\x10\xf0.\x12\x14\n\x0fWARHEAD_Nuclear\x10\xd8\x36\x12\x1c\n\x17WARHEAD_ChemicalGeneral\x10\xc0>\x12!\n\x1cWARHEAD_ChemicalBlisterAgent\x10\xa4?\x12\x1f\n\x1aWARHEAD_ChemicalBloodAgent\x10\x88@\x12\x1f\n\x1aWARHEAD_ChemicalNerveAgent\x10\xec@\x12\x1e\n\x19WARHEAD_BiologicalGeneral\x10\xa8\x46*\xdc\x01\n\x11\x44\x65tonatorFuzeEnum\x12\x0e\n\nFUZE_Other\x10\x00\x12\x11\n\x0c\x46UZE_Contact\x10\xe8\x07\x12\x18\n\x13\x46UZE_ContactInstant\x10\xcc\x08\x12\x18\n\x13\x46UZE_ContactDelayed\x10\xb0\t\x12\x0f\n\nFUZE_Timed\x10\xd0\x0f\x12\x13\n\x0e\x46UZE_Proximity\x10\xb8\x17\x12\x11\n\x0c\x46UZE_Command\x10\xa0\x1f\x12\x12\n\rFUZE_Altitude\x10\x88\'\x12\x0f\n\nFUZE_Depth\x10\xf0.\x12\x12\n\rFUZE_Acoustic\x10\xd8\x36*\xd3\x01\n\x14\x44\x65tonationResultEnum\x12\x15\n\x11\x44\x45T_RES_NO_REASON\x10\x00\x12\x15\n\x11\x44\x45T_RES_PROXIMITY\x10\x01\x12\x15\n\x11\x44\x45T_RES_AGL_LIMIT\x10\x02\x12\x15\n\x11\x44\x45T_RES_MSL_LIMIT\x10\x03\x12\x15\n\x11\x44\x45T_RES_MIN_SPEED\x10\x04\x12\x15\n\x11\x44\x45T_RES_TOF_LIMIT\x10\x05\x12\x1c\n\x18\x44\x45T_RES_COAST_TIME_LIMIT\x10\x06\x12\x13\n\x0f\x44\x45T_RES_COMMAND\x10\x07\x32\xbb\"\n\x0fJSRemoteControl\x12V\n\x11getSimStateStream\x12 .JSRemoteInterface.SimStateTypes\x1a\x1b.JSRemoteInterface.SimState\"\x00\x30\x01\x12Q\n\x12getEntityTrackList\x12\x1b.JSRemoteInterface.EntityID\x1a\x1c.JSRemoteInterface.TrackList\"\x00\x12O\n\x0egetDamageState\x12\x1b.JSRemoteInterface.EntityID\x1a\x1e.JSRemoteInterface.DamageState\"\x00\x12[\n\x15getEntityResourceData\x12\x1b.JSRemoteInterface.EntityID\x1a#.JSRemoteInterface.ResourceDataList\"\x00\x12\\\n\x16getDestroyedEntityList\x12\x1f.JSRemoteInterface.EmptyMessage\x1a\x1f.JSRemoteInterface.EntityIdList\"\x00\x12\x63\n\x18getEntityDescriptionById\x12\x1b.JSRemoteInterface.EntityID\x1a(.JSRemoteInterface.EntityTypeDescription\"\x00\x12Q\n\x13\x64oesChordHitTerrain\x12\x19.JSRemoteInterface.Points\x1a\x1d.JSRemoteInterface.Visibility\"\x00\x12s\n\x14getAeraTerrainHeight\x12+.JSRemoteInterface.AreaTerrainHeightRequest\x1a,.JSRemoteInterface.AreaTerrainHeightResponse\"\x00\x12P\n\x0cloadScenario\x12\x1f.JSRemoteInterface.ScenarioPath\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12O\n\rcloseScenario\x12\x1d.JSRemoteInterface.EmptyParam\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12G\n\x03run\x12\x1f.JSRemoteInterface.ScenarioPath\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12\x46\n\x05pause\x12\x1c.JSRemoteInterface.PauseParm\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12J\n\x06rewind\x12\x1f.JSRemoteInterface.ScenarioPath\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12W\n\x11setTimeMultiplier\x12!.JSRemoteInterface.TimeMultiplier\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12H\n\x0b\x63reateRoute\x12\x18.JSRemoteInterface.Route\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12P\n\x0f\x63reatePhaseLine\x12\x1c.JSRemoteInterface.PhaseLine\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12T\n\x11\x63reateControlArea\x12\x1e.JSRemoteInterface.ControlArea\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12R\n\x0c\x63reateEntity\x12#.JSRemoteInterface.EntitySpawnParam\x1a\x1b.JSRemoteInterface.EntityID\"\x00\x12L\n\x0cremoveEntity\x12\x1b.JSRemoteInterface.EntityID\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12S\n\x0csystemEnable\x12\".JSRemoteInterface.SystemEnableSet\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12`\n\x16setSystemRotationSpeed\x12%.JSRemoteInterface.SystemRotationTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12`\n\x16setSystemRotationAngle\x12%.JSRemoteInterface.SystemRotationTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12U\n\x0esetTrackTarget\x12\".JSRemoteInterface.TrackTargetTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12[\n\x0egetTrackTarget\x12#.JSRemoteInterface.TrackTargetQuery\x1a\".JSRemoteInterface.TrackTargetList\"\x00\x12H\n\x08stopMove\x12\x1b.JSRemoteInterface.EntityID\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12S\n\x0e\x66ixwingTakeOff\x12 .JSRemoteInterface.RouteTaskInfo\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12P\n\x0b\x66ixwingLand\x12 .JSRemoteInterface.RouteTaskInfo\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12V\n\x0cverticalLand\x12%.JSRemoteInterface.moveToLocationTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12S\n\x0emoveAlongRoute\x12 .JSRemoteInterface.RouteTaskInfo\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12X\n\x0emoveToLocation\x12%.JSRemoteInterface.moveToLocationTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12T\n\x0c\x66ollowEntity\x12#.JSRemoteInterface.FollowEntityTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12\x66\n\x15\x66lyHeadingAndAltitude\x12,.JSRemoteInterface.FlyHeadingAndAltitudeTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12I\n\x08\x66lyOrbit\x12\x1c.JSRemoteInterface.OrbitTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12O\n\x0fsetOrderedSpeed\x12\x1b.JSRemoteInterface.SpeedSet\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12W\n\x0eMoverSpeedCtrl\x12$.JSRemoteInterface.MoverSpeedCtrlSet\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12L\n\nlaseTarget\x12\x1d.JSRemoteInterface.TargetTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12N\n\x0bsetLaseCode\x12\x1e.JSRemoteInterface.LaseCodeSet\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12N\n\x0csyncLaseCode\x12\x1d.JSRemoteInterface.TargetTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12T\n\x14\x63loseLaserDesignator\x12\x1b.JSRemoteInterface.EntityID\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12Q\n\x0f\x61\x64\x64JammerTarget\x12\x1d.JSRemoteInterface.TargetTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12T\n\x12removeJammerTarget\x12\x1d.JSRemoteInterface.TargetTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12U\n\x11GetJammingTargets\x12\x1b.JSRemoteInterface.EntityID\x1a!.JSRemoteInterface.JammingTargets\"\x00\x12T\n\x0c\x66ireAtTarget\x12#.JSRemoteInterface.FireAtTargetTask\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12^\n\x18launchExpendableResource\x12!.JSRemoteInterface.LaunchResource\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12O\n\tffeTarget\x12!.JSRemoteInterface.ffeTargetParam\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12X\n\x10getWeaponResouce\x12\x1b.JSRemoteInterface.EntityID\x1a%.JSRemoteInterface.WeaponResourceList\"\x00\x12W\n\x11setSensorWorkMode\x12!.JSRemoteInterface.SystemWorkMode\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12[\n\x11getSensorWorkMode\x12!.JSRemoteInterface.SystemWorkMode\x1a!.JSRemoteInterface.SystemWorkMode\"\x00\x12V\n\x0esetSensorParam\x12#.JSRemoteInterface.SysWorkModeParam\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x12\\\n\x0eGetSensorParam\x12#.JSRemoteInterface.SysWorkModeParam\x1a#.JSRemoteInterface.SysWorkModeParam\"\x00\x12N\n\x0bshowMessage\x12\x1e.JSRemoteInterface.TextMessage\x1a\x1d.JSRemoteInterface.ResultCode\"\x00\x42=\n\x1bjiusuan.rpc.remoteinterfaceB\x16JSRemoteInterfaceProtoP\x01\xa2\x02\x03HLWb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'JSRemoteInterface_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\033jiusuan.rpc.remoteinterfaceB\026JSRemoteInterfaceProtoP\001\242\002\003HLW'
  _globals['_WEAPONSTATUSENUM']._serialized_start=10416
  _globals['_WEAPONSTATUSENUM']._serialized_end=10588
  _globals['_SENSORAIMMODEENUM']._serialized_start=10590
  _globals['_SENSORAIMMODEENUM']._serialized_end=10686
  _globals['_DEFENSEDIRECTION']._serialized_start=10689
  _globals['_DEFENSEDIRECTION']._serialized_end=10833
  _globals['_WEATHERTYPEENUM']._serialized_start=10836
  _globals['_WEATHERTYPEENUM']._serialized_end=11070
  _globals['_SIMRUNSTATEENUM']._serialized_start=11073
  _globals['_SIMRUNSTATEENUM']._serialized_end=11213
  _globals['_ARTPARTTYPEENUM']._serialized_start=11216
  _globals['_ARTPARTTYPEENUM']._serialized_end=12391
  _globals['_ENTITYKINDTYPEENUM']._serialized_start=12394
  _globals['_ENTITYKINDTYPEENUM']._serialized_end=12641
  _globals['_WEAPONSYSTEMTYPEENUM']._serialized_start=12644
  _globals['_WEAPONSYSTEMTYPEENUM']._serialized_end=12808
  _globals['_MUNITIONKINDTYPEENUM']._serialized_start=12811
  _globals['_MUNITIONKINDTYPEENUM']._serialized_end=13115
  _globals['_LIFEFORMSTATEENUM']._serialized_start=13118
  _globals['_LIFEFORMSTATEENUM']._serialized_end=13449
  _globals['_EMISSIONFUNCENUM']._serialized_start=13452
  _globals['_EMISSIONFUNCENUM']._serialized_end=15852
  _globals['_BEAMFUNCENUM']._serialized_start=15855
  _globals['_BEAMFUNCENUM']._serialized_end=16361
  _globals['_WARHEADTYPEENUM']._serialized_start=16364
  _globals['_WARHEADTYPEENUM']._serialized_end=16952
  _globals['_DETONATORFUZEENUM']._serialized_start=16955
  _globals['_DETONATORFUZEENUM']._serialized_end=17175
  _globals['_DETONATIONRESULTENUM']._serialized_start=17178
  _globals['_DETONATIONRESULTENUM']._serialized_end=17389
  _globals['_FLOATV3']._serialized_start=46
  _globals['_FLOATV3']._serialized_end=88
  _globals['_LOCATION_GEO']._serialized_start=90
  _globals['_LOCATION_GEO']._serialized_end=143
  _globals['_FLOATV3ROT']._serialized_start=145
  _globals['_FLOATV3ROT']._serialized_end=199
  _globals['_EMPTYMESSAGE']._serialized_start=201
  _globals['_EMPTYMESSAGE']._serialized_end=215
  _globals['_ENTITYID']._serialized_start=217
  _globals['_ENTITYID']._serialized_end=245
  _globals['_ENTITYIDLIST']._serialized_start=247
  _globals['_ENTITYIDLIST']._serialized_end=279
  _globals['_ENTITYINFO']._serialized_start=282
  _globals['_ENTITYINFO']._serialized_end=422
  _globals['_ENTITYINFOLIST']._serialized_start=424
  _globals['_ENTITYINFOLIST']._serialized_end=491
  _globals['_SIMSTATETYPES']._serialized_start=493
  _globals['_SIMSTATETYPES']._serialized_end=536
  _globals['_RESULTCODE']._serialized_start=538
  _globals['_RESULTCODE']._serialized_end=564
  _globals['_RESOURCEDATA']._serialized_start=566
  _globals['_RESOURCEDATA']._serialized_end=667
  _globals['_RESOURCEDATALIST']._serialized_start=669
  _globals['_RESOURCEDATALIST']._serialized_end=762
  _globals['_TRACK']._serialized_start=765
  _globals['_TRACK']._serialized_end=1187
  _globals['_TRACKID']._serialized_start=1189
  _globals['_TRACKID']._serialized_end=1247
  _globals['_TRACKLIST']._serialized_start=1249
  _globals['_TRACKLIST']._serialized_end=1326
  _globals['_SENSORSTATE']._serialized_start=1329
  _globals['_SENSORSTATE']._serialized_end=1489
  _globals['_ENTITYSENSORSTATELIST']._serialized_start=1491
  _globals['_ENTITYSENSORSTATELIST']._serialized_end=1589
  _globals['_AMMODATA']._serialized_start=1591
  _globals['_AMMODATA']._serialized_end=1635
  _globals['_WEAPONRESOURCE']._serialized_start=1638
  _globals['_WEAPONRESOURCE']._serialized_end=1839
  _globals['_WEAPONRESOURCELIST']._serialized_start=1841
  _globals['_WEAPONRESOURCELIST']._serialized_end=1931
  _globals['_AREATERRAINHEIGHTREQUEST']._serialized_start=1934
  _globals['_AREATERRAINHEIGHTREQUEST']._serialized_end=2174
  _globals['_AREATERRAINHEIGHTRESPONSE']._serialized_start=2177
  _globals['_AREATERRAINHEIGHTRESPONSE']._serialized_end=2542
  _globals['_EMPTYPARAM']._serialized_start=2544
  _globals['_EMPTYPARAM']._serialized_end=2571
  _globals['_PAUSEPARM']._serialized_start=2573
  _globals['_PAUSEPARM']._serialized_end=2599
  _globals['_SCENARIOPATH']._serialized_start=2601
  _globals['_SCENARIOPATH']._serialized_end=2629
  _globals['_TIMEMULTIPLIER']._serialized_start=2631
  _globals['_TIMEMULTIPLIER']._serialized_end=2667
  _globals['_WAYPOINT']._serialized_start=2670
  _globals['_WAYPOINT']._serialized_end=2804
  _globals['_ROUTE']._serialized_start=2806
  _globals['_ROUTE']._serialized_end=2877
  _globals['_PHASELINE']._serialized_start=2880
  _globals['_PHASELINE']._serialized_end=3011
  _globals['_CONTROLAREA']._serialized_start=3013
  _globals['_CONTROLAREA']._serialized_end=3094
  _globals['_ENTITYSPAWNPARAM']._serialized_start=3097
  _globals['_ENTITYSPAWNPARAM']._serialized_end=3281
  _globals['_ROUTETASKINFO']._serialized_start=3283
  _globals['_ROUTETASKINFO']._serialized_end=3361
  _globals['_MOVETOLOCATIONTASK']._serialized_start=3363
  _globals['_MOVETOLOCATIONTASK']._serialized_end=3452
  _globals['_FOLLOWENTITYTASK']._serialized_start=3454
  _globals['_FOLLOWENTITYTASK']._serialized_end=3552
  _globals['_FLYHEADINGANDALTITUDETASK']._serialized_start=3555
  _globals['_FLYHEADINGANDALTITUDETASK']._serialized_end=3702
  _globals['_ORBITTASK']._serialized_start=3705
  _globals['_ORBITTASK']._serialized_end=3841
  _globals['_SPEEDSET']._serialized_start=3843
  _globals['_SPEEDSET']._serialized_end=3886
  _globals['_TARGETTASK']._serialized_start=3888
  _globals['_TARGETTASK']._serialized_end=3963
  _globals['_FIREATTARGETTASK']._serialized_start=3966
  _globals['_FIREATTARGETTASK']._serialized_end=4115
  _globals['_TARGETPOINT']._serialized_start=4117
  _globals['_TARGETPOINT']._serialized_end=4197
  _globals['_JAMMERENABLESET']._serialized_start=4199
  _globals['_JAMMERENABLESET']._serialized_end=4251
  _globals['_LASEPOINTTASK']._serialized_start=4253
  _globals['_LASEPOINTTASK']._serialized_end=4337
  _globals['_LASECODESET']._serialized_start=4339
  _globals['_LASECODESET']._serialized_end=4384
  _globals['_FIREATLOCATIONTASK']._serialized_start=4387
  _globals['_FIREATLOCATIONTASK']._serialized_end=4523
  _globals['_SYSTEMENABLESET']._serialized_start=4525
  _globals['_SYSTEMENABLESET']._serialized_end=4597
  _globals['_MOVERSPEEDCTRLSET']._serialized_start=4600
  _globals['_MOVERSPEEDCTRLSET']._serialized_end=4747
  _globals['_SYSTEMROTATIONTASK']._serialized_start=4749
  _globals['_SYSTEMROTATIONTASK']._serialized_end=4862
  _globals['_JAMMINGTARGETS']._serialized_start=4864
  _globals['_JAMMINGTARGETS']._serialized_end=4948
  _globals['_LAUNCHRESOURCE']._serialized_start=4950
  _globals['_LAUNCHRESOURCE']._serialized_end=5044
  _globals['_ENTITYTYPEDESCRIPTION']._serialized_start=5046
  _globals['_ENTITYTYPEDESCRIPTION']._serialized_end=5101
  _globals['_SYSTEMWORKMODE']._serialized_start=5103
  _globals['_SYSTEMWORKMODE']._serialized_end=5181
  _globals['_TRACKTARGETTASK']._serialized_start=5183
  _globals['_TRACKTARGETTASK']._serialized_end=5306
  _globals['_SYSWORKMODEPARAM']._serialized_start=5308
  _globals['_SYSWORKMODEPARAM']._serialized_end=5405
  _globals['_TRACKTARGETQUERY']._serialized_start=5407
  _globals['_TRACKTARGETQUERY']._serialized_end=5465
  _globals['_TRACKTARGETLIST']._serialized_start=5467
  _globals['_TRACKTARGETLIST']._serialized_end=5534
  _globals['_LASERIRRCTRL']._serialized_start=5536
  _globals['_LASERIRRCTRL']._serialized_end=5639
  _globals['_EWEVENT']._serialized_start=5642
  _globals['_EWEVENT']._serialized_end=5853
  _globals['_ENTITYSTATE']._serialized_start=5856
  _globals['_ENTITYSTATE']._serialized_end=6207
  _globals['_ENTITYSTATELIST']._serialized_start=6209
  _globals['_ENTITYSTATELIST']._serialized_end=6280
  _globals['_SIMSTATE']._serialized_start=6283
  _globals['_SIMSTATE']._serialized_end=6787
  _globals['_WEATHERINFO']._serialized_start=6790
  _globals['_WEATHERINFO']._serialized_end=7042
  _globals['_SEAINFO']._serialized_start=7045
  _globals['_SEAINFO']._serialized_end=7209
  _globals['_SIMRUNSTATE']._serialized_start=7212
  _globals['_SIMRUNSTATE']._serialized_end=7463
  _globals['_DATETIME']._serialized_start=7465
  _globals['_DATETIME']._serialized_end=7563
  _globals['_ARTICULATEDPARTSTATE']._serialized_start=7566
  _globals['_ARTICULATEDPARTSTATE']._serialized_end=7881
  _globals['_LASERDESIGNATOREVT']._serialized_start=7884
  _globals['_LASERDESIGNATOREVT']._serialized_end=8093
  _globals['_EMITTERBEAMSTATE']._serialized_start=8096
  _globals['_EMITTERBEAMSTATE']._serialized_end=8499
  _globals['_FIREEVENT']._serialized_start=8502
  _globals['_FIREEVENT']._serialized_end=8915
  _globals['_FFETARGETPARAM']._serialized_start=8918
  _globals['_FFETARGETPARAM']._serialized_end=9156
  _globals['_DETONATIONEVENT']._serialized_start=9159
  _globals['_DETONATIONEVENT']._serialized_end=9404
  _globals['_MESSAGESENDEVENT']._serialized_start=9407
  _globals['_MESSAGESENDEVENT']._serialized_end=9568
  _globals['_MESSAGERECVEVENT']._serialized_start=9570
  _globals['_MESSAGERECVEVENT']._serialized_end=9684
  _globals['_MESSAGEEVENT']._serialized_start=9687
  _globals['_MESSAGEEVENT']._serialized_end=9836
  _globals['_ENTITYOPTEVENT']._serialized_start=9838
  _globals['_ENTITYOPTEVENT']._serialized_end=9941
  _globals['_DAMAGESTATE']._serialized_start=9943
  _globals['_DAMAGESTATE']._serialized_end=9978
  _globals['_TEXTMESSAGE']._serialized_start=9980
  _globals['_TEXTMESSAGE']._serialized_end=10010
  _globals['_SENSORLIST']._serialized_start=10012
  _globals['_SENSORLIST']._serialized_end=10075
  _globals['_SENSORINFO']._serialized_start=10077
  _globals['_SENSORINFO']._serialized_end=10119
  _globals['_POINTS']._serialized_start=10121
  _globals['_POINTS']._serialized_end=10227
  _globals['_VISIBILITY']._serialized_start=10229
  _globals['_VISIBILITY']._serialized_end=10261
  _globals['_SENSOREVENT']._serialized_start=10264
  _globals['_SENSOREVENT']._serialized_end=10413
  _globals['_JSREMOTECONTROL']._serialized_start=17392
  _globals['_JSREMOTECONTROL']._serialized_end=21803
# @@protoc_insertion_point(module_scope)
