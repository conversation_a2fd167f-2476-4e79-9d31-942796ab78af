# 添加目录
import sys
import os
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)  
# 导入数据
from proto import JSRemoteInterface_pb2 as pb2
import uuid

class Action:
    def __init__(self):
        pass
    
    @staticmethod           # 可以  类名.方法名 对象.方法名 调用
    def get_command(action_type, command):
        return {
            'action_type': action_type,
            'command': command
        }

    # 移动控制指令
    @staticmethod
    def stopMove():

        pass






    
// 停止移动
rpc stopMove(EntityID) returns(ResultCode) {}

// 固定翼起飞
rpc fixwingTakeOff(RouteTaskInfo) returns (ResultCode) {}

// 固定翼着陆
rpc fixwingLand(RouteTaskInfo) returns (ResultCode) {}

// 垂直着陆
rpc verticalLand(moveToLocationTask) returns (ResultCode) {}

// 沿路线移动
rpc moveAlongRoute(RouteTaskInfo) returns (ResultCode) {}

// 移动到指定位置
rpc moveToLocation(moveToLocationTask) returns (ResultCode) {}

// 跟随实体
rpc followEntity(FollowEntityTask) returns (ResultCode) {}

// 按航向和高度飞行
rpc flyHeadingAndAltitude(FlyHeadingAndAltitudeTask) returns (ResultCode) {}

// 绕轨道飞行
rpc flyOrbit(OrbitTask) returns (ResultCode) {}

// 设置速度
rpc setOrderedSpeed(SpeedSet) returns (ResultCode) {}

// 移动速度控制
rpc MoverSpeedCtrl(MoverSpeedCtrlSet) returns (ResultCode) {}