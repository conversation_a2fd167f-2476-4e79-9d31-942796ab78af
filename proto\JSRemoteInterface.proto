syntax = "proto3";

option java_multiple_files = true;
option java_package = "jiusuan.rpc.remoteinterface";
option java_outer_classname = "JSRemoteInterfaceProto";
option objc_class_prefix = "HLW";



package JSRemoteInterface;

// Interface that USIM Service support 
service JSRemoteControl {
  ///////////////////////////////////////////stream data 
  // 1.1 Gloabl Sim Data
  //    1) SimState (1time per second)  
  //    2) Simulation events: fire, explosion, destruction, communication
  //    3) States of all simulation entities in the simulation (basic information)
  //    4) Working states of all detectors in the simulation
  rpc getSimStateStream(SimStateTypes) returns (stream SimState) {}
  
  ///////////////////////////////////////////State query
  //2.1 Targets detected by the entity's sensors
  rpc getEntityTrackList(EntityID) returns(TrackList) {}
  
  //2.2 Query damage status
  rpc getDamageState(EntityID) returns(DamageState){}

  //2.3 Query entity resource data
  rpc getEntityResourceData(EntityID) returns(ResourceDataList){}
  
  //2.4  
  rpc getDestroyedEntityList(EmptyMessage) returns (EntityIdList) {}
  
  //2.5
  rpc getEntityDescriptionById(EntityID) returns(EntityTypeDescription){}
  
  ///////////////////////////Terrain
  //2.6 
  rpc doesChordHitTerrain(Points) returns (Visibility) {}
  
  //2.7 Elevation data of the specified area
  rpc getAeraTerrainHeight(AreaTerrainHeightRequest) returns(AreaTerrainHeightResponse){}
  
  /////////////////////////////////////////// Simulation Control
  //3.1 Load scenario
  rpc loadScenario(ScenarioPath) returns (ResultCode) {}
  //3.2 Close scenario
  rpc closeScenario(EmptyParam) returns (ResultCode) {}
  //3.3 Start running
  rpc run(ScenarioPath) returns (ResultCode) {}
  //3.4 Pause
  rpc pause(PauseParm) returns (ResultCode) {}
  //3.5 Reset scenario
  rpc rewind(ScenarioPath) returns (ResultCode) {}
  //3.6 Set simulation speed multiplier
  rpc setTimeMultiplier(TimeMultiplier) returns(ResultCode) {}

  ///////////////////////////////////////////Create Something
  //4.1. 
  rpc createRoute(Route) returns (ResultCode) {}
  //4.2. 
  rpc createPhaseLine(PhaseLine) returns (ResultCode) {}
  //4.3. 
  rpc createControlArea(ControlArea) returns (ResultCode) {}
  //4.4. 
  rpc createEntity(EntitySpawnParam) returns(EntityID){}
  //4.5. 
  rpc removeEntity(EntityID) returns(ResultCode){}
  //////////////////////////////////////////General System Control
  //5.1
  rpc systemEnable(SystemEnableSet) returns (ResultCode) {}
  //5.2  Only Work On ArticularPart System
  rpc setSystemRotationSpeed(SystemRotationTask)  returns (ResultCode) {}
  //5.3. 
  rpc setSystemRotationAngle(SystemRotationTask)  returns (ResultCode) {}
  //5.4 start or stop track target
  rpc setTrackTarget(TrackTargetTask) returns (ResultCode) {}
  //5.5
  rpc getTrackTarget(TrackTargetQuery) returns (TrackTargetList) {}
  /////////////////////////////////////////// Mover Control
  //6.1. 
  rpc stopMove(EntityID) returns(ResultCode) {}
  //6.2. 
  rpc fixwingTakeOff(RouteTaskInfo) returns (ResultCode) {}
  //6.3. 
  rpc fixwingLand(RouteTaskInfo) returns (ResultCode) {}
  //6.4. 
  rpc verticalLand(moveToLocationTask) returns (ResultCode) {}
  //6.5. 
  rpc moveAlongRoute(RouteTaskInfo) returns (ResultCode) {}
  //6.6. 
  rpc moveToLocation(moveToLocationTask) returns (ResultCode) {}
  //6.7.
  rpc followEntity(FollowEntityTask) returns (ResultCode) {}
  //6.8. 
  rpc flyHeadingAndAltitude(FlyHeadingAndAltitudeTask) returns (ResultCode) {}
  //6.9. 
  rpc flyOrbit(OrbitTask) returns (ResultCode) {}
  //6.10. 
  rpc setOrderedSpeed(SpeedSet) returns (ResultCode) {}
  //6.11. 
  rpc MoverSpeedCtrl(MoverSpeedCtrlSet) returns (ResultCode) {}
  
  //////////////////////////////////Laser Control
  //7.1 
  rpc laseTarget(TargetTask) returns (ResultCode) {}
  //7.2
  rpc setLaseCode(LaseCodeSet) returns (ResultCode) {}
  //7.3. Synchronize laser code with a certain entity (enable laser guidance for other entities)
  rpc syncLaseCode(TargetTask) returns (ResultCode) {}
  //7.4. 
  rpc closeLaserDesignator(EntityID) returns (ResultCode) {}
  ////////////////////////////////Jammer Control
  //8.1 
  rpc addJammerTarget(TargetTask) returns (ResultCode) {}
  //8.2 
  rpc removeJammerTarget(TargetTask) returns (ResultCode) {}
  //8.3 
  rpc GetJammingTargets(EntityID) returns (JammingTargets) {}
  
  /////////////////////////////Weapon Control
  //9.1. 
  rpc fireAtTarget(FireAtTargetTask)  returns (ResultCode) {}
  //9.2. 
  rpc launchExpendableResource(LaunchResource) returns (ResultCode) {}
  //9.3. 
  rpc ffeTarget(ffeTargetParam) returns (ResultCode){}
  //9.4 Weapon working status of the entity
  rpc getWeaponResouce(EntityID) returns(WeaponResourceList) {}
  ///////////////////////////////////////General Sensor Control
  //10.1
  rpc setSensorWorkMode(SystemWorkMode) returns(ResultCode) {}
  //10.2
  rpc getSensorWorkMode(SystemWorkMode) returns (SystemWorkMode) {}
  //10.3
  rpc setSensorParam(SysWorkModeParam) returns (ResultCode) {}
  //10.4
  rpc GetSensorParam(SysWorkModeParam) returns (SysWorkModeParam) {}
  ///////////////////////////////////////Radar Control
  
  /////////////////////////////////////////////EW Control
  
  /////////////////////////////////////////// UI
  //11.1
  rpc showMessage(TextMessage)returns(ResultCode) {}

}

/////////////////////////////////////////common///////////////////////////////////////////
message FloatV3{
  float x = 1;
  float y = 2;
  float z = 3;
}
message Location_GEO{
  double lat = 1;
  double lon = 2;
  double alt = 3;
}
message FloatV3Rot{
  float yaw = 1;    //! about reference z
  float pitch = 2;    //! about intermediate y
  float roll = 3;    //! about body x
}

message EmptyMessage{}

//unique id of entity,input param
message EntityID{
  string entityId = 1;
}

message EntityIdList{
  repeated string entityId = 1;
}

//
message EntityInfo{
  string entityId = 1;
  string distype= 2;
  string displayName = 3;
  ResourceDataList resouceList= 4;
  string team= 5;
}

message EntityInfoList{
    repeated EntityInfo entityList= 1;
}

//

//as getSimStateStream Input，we will filter simstate message by this vaule
message SimStateTypes{
  string userPermissionCode = 1;
}

message ResultCode{
  int32 code = 1; //0:sucess， other:error code
}



/////////////////////////////////////////////////////////state query
//single resource
message ResourceData{
  float  currentAmount = 1;   //
  float  fullAmount = 2;      //
  string ResourceName = 3;            //
  string   resourceType = 4;  //
}

//resource list
message ResourceDataList{
  string ownerEntityId = 1;
  repeated ResourceData resources = 2;
}

//
message Track{
  TrackID       trackid = 1;  //
  float        TrackQuality = 2;
  string       targetId = 3;       //can be null              
  FloatV3          NEDvelocity = 4;           //
  Location_GEO         location = 5;           //LLA
  FloatV3Rot     orientation = 6;       //
  string    entityType = 7;          //
  double           targetAzimuth = 8;
  double           targetElevation = 9;
  Location_GEO         refLocation = 10;
  string           TrackSourceEntityID = 11;
  string         trackSourceSystem = 12;
}

message TrackID{
 string OwningEntiyID = 1;
 int32 LocalTrackNumber = 2;
}

message TrackList{
  string sourceEntityId = 1;        //
  repeated Track tracks = 2; //
}

message SensorState
{
  string sensorName = 1;
  string sensorType = 2;
  bool isTurnOn = 3;
  string workmode = 4;
  FloatV3Rot orientation = 5; 
  bool isDamaged = 6;
}

message EntitySensorStateList
{
  string entityId = 1;
  repeated SensorState sensorStateList = 2;
}

//
enum WeaponStatusEnum{
  OK_TO_FIRE = 0; //
  DISABLED = 1;  //
  NO_AMMUNITION_FOR_TARGET = 2;  //
  CANNOT_ACQUIRE_TARGET = 4;  //
  SUPPRESSED = 8;  //
  OUT_OF_AMMUNITION_FOR_WEAPON = 16; //
  WEAPON_BUSY = 32; // Launcher still tracking a missile, can't launch another
}

message AmmoData
{
  string AmmoType = 1; //dis std
  int32 remain = 2;
}

//
message WeaponResource{
  string weaponName = 1;
  WeaponSystemTypeEnum weaponType = 2;
  WeaponStatusEnum status = 3;
  repeated AmmoData AmmoDataList = 4;
}
//
message WeaponResourceList{
  string entityId = 1;
  repeated WeaponResource weapons = 2;
}

//
message AreaTerrainHeightRequest{
  Location_GEO southwestCorner = 1;
  Location_GEO northeastCorner = 2;
  float latInterval = 3;
  float lonInterval = 4;
  double latDegreesInterval = 5;
  double lonDegreesInterval = 6;
}

//
message AreaTerrainHeightResponse{
  Location_GEO southwestCorner = 1;
  Location_GEO northeastCorner = 2;
  double   longitudeSampleInterval = 3; //
  double   longitudeSampleDegreesInterval = 4; //DEG
  int32    longitudeSampleNumber = 5; //
  double   latitudeSampleInterval = 6; //
  double   latitudeSampleDegreesInterval = 7; //DEG
  int32    latitudeSampleNumber = 8; //
  repeated double  heights = 9; //FROM South to north,form west to east 
}
/////////////////////////////////////Input Control Data////////////////////////////////////
message EmptyParam{
  string empty = 1; 
}

message PauseParm{
    bool pause = 1;
}
message ScenarioPath{
  string path = 1; // 
}
message TimeMultiplier{
  float multiplier = 1; //
}
//
message Waypoint{
  string labalId = 1;     //waypoint unique id
  Location_GEO LLA = 2;
  float arriveSpeed = 3; //m/s
  float pauseTime = 4; //sec
  string nextWpLabal = 5; //when the waypoint reached,this will be the next waypoint
}
//
message Route{
  string nameId = 1;                 //unique id
  repeated Waypoint waypoints = 2; //LLA
}
//
message PhaseLine{
  string nameId = 1;                 //unique id
  Location_GEO location1 = 2;          //LLA Of First Point lat,lon,alt
  Location_GEO location2 = 3;          //LLA Of Second Point
}
//
message ControlArea{
  string nameId = 1;                 //unique id
  repeated Location_GEO locations = 2; //LLA
}


message EntitySpawnParam {
  string entityId = 1;
  string entityName=2;
  string entityType = 3;    //
  string forceType = 4;     //TeamFlag
  Location_GEO location = 5;   //LLA
  float heading = 6;        //referernce to north
  string  commanderID = 7;
}

// 
message RouteTaskInfo{
  string entityId = 1;      //
  Route routeInfo = 2;       //
}
// 
message moveToLocationTask{
  string entityId = 1;      //
  Location_GEO location = 2;    //LLA
}
//
message FollowEntityTask{
  string entityId = 1;  //
  string leaderId = 2;  //
  FloatV3 offset = 3;   //meter,front/right/down is positive
}
//
message FlyHeadingAndAltitudeTask{
  string entityId = 1;  //
  float  heading = 2;  //dif from north in rad
  float  turnRate = 3;  // rad/s
  float  altitude = 4;  //meter
  float  climbDescentRate = 5; // m/s
  sint32  turnDirection = 6;   //0:samllest turn angle,1:turn right,-1:turn left
}
//
message OrbitTask{
  string      entityId = 1;  //
  Location_GEO  center = 2;    //LLA
  string      entityToOrbit = 3; //if this set,ignore center
  float      radius = 4;        //meter
  bool      clockwise = 5;     //
}
//
message  SpeedSet{
  string      entityId = 1;  //
  float       speed = 2;     //m/s
}

message  TargetTask{
  string      entityId = 1;  //
  TrackID     trackId = 2;   
}
//
message FireAtTargetTask{
  string            entityId = 1;         //
  string            weaponToFire = 2;     //can be null,which will automaticly choose a weapon to fire
  TrackID            targettrack = 3 ;       //
  int32             maxRoundsToFire = 4;  //
  string routeID = 5;  //optional , only usefull if missile support
}

//
message TargetPoint{
  string   nameId = 1;     //
  Location_GEO location = 2;   //LLA
}


message JammerEnableSet{
  string entityId = 1;         //
  bool   enanble = 2;         //
}

//
message LasePointTask{
  string entityId = 1;              //
  Location_GEO  location = 2;         //LLA
}

//
message LaseCodeSet{
  string entityId = 1;         //
  int32  code = 2;             //
}
//
message FireAtLocationTask{
  string      entityId = 1;         //
  Location_GEO  location = 2;         //LLA
  string            weaponToFire = 3;     //
  int32             maxRoundsToFire = 4;  //
}

//nzy mark
enum SensorAimModeEnum{
  FIXED_ANGLE = 0;     // 
  TRACK_ENTITY = 1;    // 
  TRACK_LOCATION = 2;  // 
  SCAN = 3;            // 
  MANUAL = 4;          // 
};



//
message SystemEnableSet{
  string      entityId = 1;         //
  string      systemName = 2;       //
  bool        enanble = 3;       //
}

//
message MoverSpeedCtrlSet{
  string      entityId = 1;         //
  FloatV3 targetLineSpeedNED = 2;    //m/s
  FloatV3 targetAngleSpeed = 3;   //rad/s
}

message SystemRotationTask{
  string entityId = 1;
  string systemName = 2;
  FloatV3Rot targetRotation = 3;  //can be speed or angle
}

//nzy mark
message JammingTargets{
  string entityId = 1;
  repeated TrackID targetTracks = 2;
}

message LaunchResource {
  string entityId = 1;
  string resourcename = 2;
  int32 number = 3;
  double timeInterval = 4;
}

message EntityTypeDescription{
  string entityId = 1;	
  string desc = 2;
}

message SystemWorkMode{
  string entityId = 1;
  string systemNameId = 2;
  string WorkModeName = 3;
}

message TrackTargetTask{
  string entityId = 1;
  string systemNameId = 2;
  TrackID trackId = 3;
  bool StartorStop = 4;
}
message SysWorkModeParam{
  string entityId = 1;
  string SystemNameId = 2;
  string paramName = 3;
  string paramValue = 4;
}

message TrackTargetQuery{
  string entityId = 1;
  string systemNameId = 2;
}

message TrackTargetList{
  repeated TrackID trackTargets = 1;
}

message LaserIrrCtrl{
  double rangingFreq = 1;
  uint32 laserIrrCode = 2;
  uint32 laserIrrDelay = 3;
  uint32 laserIrrCycle = 4;
}

//nzy mark
message EWEvent{
  uint32 lotNum = 1;
  uint32 state = 2;
  uint32 platType = 3;
  uint32 iff = 4;
  string platModel = 5;
  double azi = 6;
  double ele = 7;
  bool isRadarWarning = 8;
  bool isMissileWarning = 9;
  bool isLaserWarning = 10;
  double threatLevel = 11;
}

enum DefenseDirection{
  AUTO_DIRECTION = 0;
  FRONT = 1;
  LEFT_FRONT = 2;
  LEFT = 3;
  LEFT_BACK = 4;
  BACK = 5;
  RIGHT_BACK = 6;
  RIGHT = 7;
  RIGHT_FRONT = 8;
}

////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////
////
message EntityState{
  string           entityId = 1;      //
  string    forceType = 2;     //
  string    entityType = 3;    //
  float       speed = 4;      // m/s linespeed
  FloatV3          velocity = 5;      //m/s ned
  Location_GEO         location = 6;      //LLA
  FloatV3Rot     orientation = 7;   //deg 
  string           displayname = 8;    //visual
  float    damage = 9;   //0-1
  float           agl = 10;//m
  bool            isEmbarked = 11;              //
  string          embarkedOnEntityId = 12;    //If isEmbarked true
  string     commander = 13;
}

message EntityStateList{
  repeated EntityState entityStates = 1;

}

//global sim data
message SimState{
  oneof state{
    SimRunState simRunState = 1;
    EntityStateList entityStates = 2;
    LaserDesignatorEvt ldEvt = 3;
    FireEvent fireEvt = 4; //
    DetonationEvent detonationEvt = 5; //
    MessageEvent MsgEvt =6;
    EntityOptEvent entityOpt = 8;  //
    EWEvent ewEvt = 9;
    SensorEvent SenorEvt = 10;
  }
}
/////////////////////////////////////Running State////////////////////////////////////


enum WeatherTypeEnum {
  LIGHTRAIN = 0; //
  RAIN = 1; //
  HEAVYRAIN = 2; //
  THUNDERSTORM = 3; //
  LIGHTSNOW = 4; //
  SNOW = 5; //
  HEAVYSNOW = 6; //
  BIZZARD = 7; //
  DUSTSTORM = 8; //
  DUST = 9; //
  FOGGY = 10;
  CLEARSKY = 11;
  PARTLYCLOUDY = 12;
  CLOUDY = 13;
  OVERCAST = 14;
  CUSTOM = 15;
};

message WeatherInfo{
  WeatherTypeEnum weatherType = 1;
  float rain = 2;     //0-1
  float snow = 3;    //0-1
  float fog = 4;    //0-1
  float dust = 5;    //0-1
  float cloud = 6;   //0-1
  float visibility = 7;                  //meter
  float fogHeight = 8;                   //meter
  float windSpeed = 9;                   //m/s
  float windDirection = 10;               //diff from north
  float ambientAirTemperature = 11;        //s-deg
}

message SeaInfo{
  float tidalOffset = 1;                 //meter
  float tidalDirection = 2;              //diff from north
  float swellState = 3;                  //0-9
  float underwaterVisibility = 4;        //meter
  float waterCurrentSpeed = 5;           // m/s
  float ambientWaterTemperature = 6;     //s deg
}

enum SimRunStateEnum{
    PENDING_INITIALIZE = 0;//!< The simulation has been constructed and is ready for 
                            // WsfSimulation::Initialize to be called.
    INITIALIZING = 1; //!< The WsfSimulation::Initialize method is being called.
    PENDING_START = 2; //!< Initialization is complete, ready for WsfSimulation::Start to be called.
    STARTING = 3; //!< The WsfSimulation::Start method is being called.
    ACTIVE = 4; //!< Start is complete, the simulation is in progress.
    PENDING_COMPLETE = 5; //!< Simulation processing is complete; waiting on a call to WsfSimulation::Complete
    COMPLETE = 6;//!< Simulation is complete.
}

message SimRunState{
  SimRunStateEnum  runingState = 1;                     //
  float timeMultiplier = 2;             //
  double simtime = 3;          //second
  dateTime simDateTime = 4;        //
  WeatherInfo weather = 5;            //
  SeaInfo sea = 6;
}

message dateTime{
  int32 year = 1;
  int32 month = 2;
  int32 day = 3;
  int32 hour = 4;
  int32 minute = 5;
  int32 second = 6;
}

/////////////////////////////////////Entity state////////////////////////////////////
//ArtPartType (Follow DIS standerd)
enum ArtPartTypeEnum{
  DtEntityBase = 0;
  DtRudder = 1024;  //
  DtLeftFlap = 1056;  //
  DtRightFlap = 1088;  //
  DtLeftAileron = 1120;  //
  DtRightAileron = 1152;  //
  DtHelicopterMainRotor = 1184;  //
  DtHelicopterTailRotor = 1216;  //
  DtOtherAircraftControl = 1248;  //

  DtPeriscope = 2048;  //
  DtGenericAntenna = 2080;  //
  DtSnorkel = 2112;  //
  DtOtherSubExtendable = 2144;  //

  DtLandingGear = 3072;  //
  DtTailHook = 3104;  //
  DtSpeedBrake = 3136;  //
  DtLeftWeaponBayDoor = 3168;  //
  DtRightWeaponBayDoor = 3200;  //
  DtTankAPCHatch = 3232;  //
  DtWingSweep = 3264;  //

  DtPrimaryTurret1 = 4096;  //
  DtPrimaryTurret2 = 4128;  //
  DtPrimaryTurret3 = 4160;  //

  DtPrimaryGun1 = 4416;  //
  DtPrimaryGun2 = 4448;  //
  DtPrimaryGun3 = 4480;  //

  DtPrimaryLauncher1 = 4736;  //
  DtPrimaryLauncher2 = 4768;  //
  DtPrimaryLauncher3 = 4800;  //

  DtPrimaryDefenseSys1 = 5056;  //
  DtPrimaryDefenseSys2 = 5088;  //
  DtPrimaryDefenseSys3 = 5120;  //

  DtPrimaryRadar1 = 5376;  //
  DtPrimaryRadar2 = 5408;  //
  DtPrimaryRadar3 = 5440;  //

  DtSecondaryTurret1 = 5696;  //
  DtSecondaryTurret2 = 5728;  //
  DtSecondaryTurret3 = 5760;  //

  DtSecondaryGun1 = 6016;  //
  DtSecondaryGun2 = 6048;  //
  DtSecondaryGun3 = 6080;  //

  DtSecondaryLauncher1 = 6336;  //
  DtSecondaryLauncher2 = 6368;  //
  DtSecondaryLauncher3 = 6400;  //

  DtSecondaryDefenseSys1 = 6656;  //
  DtSecondaryDefenseSys2 = 6688;  //
  DtSecondaryDefenseSys3 = 6720;  //

  DtSecondaryRadar1 = 6976;   //
  DtSecondaryRadar2 = 7008;   //
  DtSecondaryRadar3 = 7040;   //
};

//
message ArticulatedPartState{
  ArtPartTypeEnum      partType = 1;     //
  uint32               attachedToPartIndex = 2;   //
  FloatV3              translation = 3;           //
  FloatV3              translationRate = 4;       //
  FloatV3Rot        orientation = 5;           //
  FloatV3Rot        orientationRate = 6;       //
}

//
enum EntityKindTypeEnum{
  EKT_OTHER_KIND = 0;           //
  EKT_PLATFORM_KIND = 1;        //
  EKT_MUNITION_KIND = 2;        //
  EKT_LIFEFORM_KIND = 3;        //
  EKT_ENVIRONMENTAL_KIND = 4;   //
  EKT_CULTURAL_KIND = 5;        //
  EKT_SUPPLY_KIND = 6;          //
  EKT_RADIO_KIND = 7;           //
  EKT_EXPENDABLE_KIND = 8;      //
  EKT_SENSOR_KIND = 9;          //
}



//
enum WeaponSystemTypeEnum{
  WEAPON_ALLTYPE = 0;
  WEAPON_DIRECTFIRE_BALLISTIC = 1; //Guns, aircraft cannons, etc., which can target specific objectives.
  WEAPON_DIRECTFIRE_LAUNCHER = 2; //missile
  WEAPON_INDIRECT_ARTILLERY = 3;   //Artillery barrages, howitzers, etc
  WEAPON_INDIRECT_BOMB = 4;        //air bomb
};
//
enum MunitionKindTypeEnum{
  MKT_OTHER = 0;
  MKT_ANTIAIR = 1;
  MKT_ANTIARMOR = 2;
  MKT_ANTIGUIDED_WEAPON = 3;
  MKT_ANTIRADAR = 4;
  MKT_ANTISATELLITE = 5;
  MKT_ANTISHIP = 6;
  MKT_ANTISUBMARINE = 7;
  MKT_ANTIPERSONNEL = 8;
  MKT_BATTLEFIELD_SUPPORT = 9;
  MKT_STRATEGIC = 10;
  MKT_TACTICAL = 11;
  MKT_DIRECTED_ENERGY_WEAPON = 12;
};


//nzy mark
enum LifeformStateEnum
{
  LIFEFORM_NA = 0;
  LIFEFORM_STANDING = 1; //
  LIFEFORM_WALKING = 2;  //
  LIFEFORM_RUNNING = 3;  //
  LIFEFORM_KNEELING = 4; //
  LIFEFORM_PRONE = 5;    //
  LIFEFORM_CRAWLING = 6; //
  LIFEFORM_SWIMMING = 7; //
  LIFEFORM_PARACHUTING = 8; //
  LIFEFORM_JUMPING = 9;     //
  LIFEFORM_SITTING = 10;    //
  LIFEFORM_SQUATTING = 11;  //
  LIFEFORM_CROUCHING = 12;  //
  LIFEFORM_WADING = 13;     //
};

////////////////////////////////////////LaserDesignatorState///////////////////////////////////////////////////
message LaserDesignatorEvt{
  string                  sourceEntityId = 1;     //
  string                  designatedEntityId = 2; //
  uint32                  code = 3;               //
  float                   power = 4;              //WALT
  float                   wavelength = 5;         //
  Location_GEO                location = 6;           //LLA
  bool                    powerState = 7;         // True:On，False:Off
  double       simTime = 8;
}



////////////////////////////////////////Radar Work state///////////////////////////////////////////////////
enum EmissionFuncEnum{
  EMISSION_FUN_cOther = 0;
  EMISSION_FUN_cMultiFunction = 1;
  EMISSION_FUN_cEarlyWarningSurveillance = 2;
  EMISSION_FUN_cHeightFinding = 3;
  EMISSION_FUN_cFireControl = 4;
  EMISSION_FUN_cAcquisitionDetection = 5;
  EMISSION_FUN_cTracking = 6;
  EMISSION_FUN_cGuidanceIllumination = 7;
  EMISSION_FUN_cFiringPointLaunchPointLocation = 8;
  EMISSION_FUN_cRanging = 9;
  EMISSION_FUN_cRadarAltimeter = 10;
  EMISSION_FUN_cImaging = 11;
  EMISSION_FUN_cMotionDetection = 12;
  EMISSION_FUN_cNavigation = 13;
  EMISSION_FUN_cWeather = 14;
  EMISSION_FUN_cInstrumentation = 15;
  EMISSION_FUN_cIdentificationClassification = 16;
  EMISSION_FUN_cAAA_Anti_Aircraft_Artillery_Fire_Control = 17;
  EMISSION_FUN_cAir_Search_Bomb = 18;
  EMISSION_FUN_cAir_Intercept = 19;
  EMISSION_FUN_cAltimeter = 20;
  EMISSION_FUN_cAir_Mapping = 21;
  EMISSION_FUN_cAir_Traffic_Control = 22;
  EMISSION_FUN_cBeacon = 23;
  EMISSION_FUN_cBattlefield_Surveillance = 24;
  EMISSION_FUN_cGround_Control_Approach = 25;
  EMISSION_FUN_cGround_Control_Intercept = 26;
  EMISSION_FUN_cCoastal_Surveillance = 27;
  EMISSION_FUN_cDecoy_Mimic = 28;
  EMISSION_FUN_cData_Transmission = 29;
  EMISSION_FUN_cEarth_Surveillance = 30;
  EMISSION_FUN_cGun_Lay_Beacon = 31;
  EMISSION_FUN_cGround_Mapping = 32;
  EMISSION_FUN_cHarbor_Surveillance = 33;
  EMISSION_FUN_cILS_Instrument_Landing_System = 35;
  EMISSION_FUN_cIonospheric_Sound = 36;
  EMISSION_FUN_cInterrogator = 37;
  EMISSION_FUN_cBarrage_Jamming = 38;
  EMISSION_FUN_cClick_Jamming = 39;
  EMISSION_FUN_cFrequency_Swept_Jamming = 41;
  EMISSION_FUN_cJamming = 42;
  EMISSION_FUN_cPulsed_Jamming = 44;
  EMISSION_FUN_cRepeater_Jamming = 45;
  EMISSION_FUN_cSpot_Noise_Jamming = 46;
  EMISSION_FUN_cMissile_Acquisition = 47;
  EMISSION_FUN_cMissile_Downlink = 48;
  EMISSION_FUN_cSpace = 50;
  EMISSION_FUN_cSurface_Search = 51;
  EMISSION_FUN_cShell_Tracking = 52;
  EMISSION_FUN_cTelevision = 56;
  EMISSION_FUN_cUnknown = 57;
  EMISSION_FUN_cVideo_Remoting = 58;
  EMISSION_FUN_cExperimental_or_Training = 59;
  EMISSION_FUN_cMissile_Guidance = 60;
  EMISSION_FUN_cMissile_Homing = 61;
  EMISSION_FUN_cMissile_Tracking = 62;
  EMISSION_FUN_cJammingNoise = 64;
  EMISSION_FUN_cJammingDeception = 65;
  EMISSION_FUN_cDecoy = 66;
  EMISSION_FUN_cNavigation_Distance_Measuring_Equipment = 71;
  EMISSION_FUN_cTerrain_Following = 72;
  EMISSION_FUN_cWeather_Avoidance = 73;
  EMISSION_FUN_cProximity_Fuse = 74;
  EMISSION_FUN_cRadiosonde = 76;
  EMISSION_FUN_cSonobuoy = 77;
  EMISSION_FUN_cWeaponNonLethal = 96;
  EMISSION_FUN_cWeaponLethal = 97;
};

//
enum BeamFuncEnum{
  BEAMFUNC_Other = 0;
  BEAMFUNC_Search = 1;   //
  BEAMFUNC_HeightFinder = 2;   //
  BEAMFUNC_Acquisition = 3;   //
  BEAMFUNC_Tracking = 4;   //
  BEAMFUNC_AcquisitionAndTracking = 5;   //
  BEAMFUNC_CommandGuidance = 6;   //
  BEAMFUNC_Illumination = 7;   //
  BEAMFUNC_RangeOnlyRadar = 8;   //
  BEAMFUNC_MissileBeacon = 9;   //
  BEAMFUNC_MissileFuze = 10;  //
  BEAMFUNC_ActiveRadarMissileSeeker = 11;  //
  BEAMFUNC_Jammer = 12;  //
  BEAMFUNC_IFF = 13;  //
  BEAMFUNC_NavWeather = 14;  //
  BEAMFUNC_Meteorological = 15;  //
  BEAMFUNC_DataTransmission = 16;  //
  BEAMFUNC_NavDirectionalBeacon = 17;  //
};

message EmitterBeamState{
  float              azimuthCenter = 1;  //
  float              azimuthSweep = 2;   //
  float              elevationCenter = 3; //
  float              elevationSweep = 4;  //
  BeamFuncEnum       beamFunction = 5;   //
  uint32             beamStatus = 6;     //
  uint32             beamId = 7;         //
  float              ERP = 8;             //
  float              frequency = 9;      //
  float              frequencyRange = 10; //
  float              PRF = 11;            //
  float              pulseWidth = 12;    //
  float              sweepSync = 13;      //
  bool               highDensity = 14;          //
  repeated  string   trackJamTargets = 15;      //
  bool               jamBeam = 16;              //
  uint32             jammingModeSequence = 17;  //
};


////////////////////////////////////////FireEvent///////////////////////////////////////////////////
//WarheadType,Follow DIS Standerd
enum WarheadTypeEnum{
  WARHEAD_Other = 0;
  WARHEAD_HighExplosive = 1000;  //
  WARHEAD_HighExplosivePlastic = 1100;  //
  WARHEAD_HighExplosiveIncendiary = 1200;  //
  WARHEAD_HighExplosiveFragmentation = 1300; //
  WARHEAD_HighExplosiveAntiTank = 1400;  //
  WARHEAD_HighExplosiveBomblets = 1500;  //
  WARHEAD_HighExplosiveShapedCharge = 1600;  //
  WARHEAD_Smoke = 2000;  //
  WARHEAD_Illumination = 3000;  //
  WARHEAD_Practice = 4000;  //
  WARHEAD_Kinetic = 5000;  //
  WARHEAD_Unused = 6000;
  WARHEAD_Nuclear = 7000;  //
  WARHEAD_ChemicalGeneral = 8000;  //
  WARHEAD_ChemicalBlisterAgent = 8100;  //
  WARHEAD_ChemicalBloodAgent = 8200;  //
  WARHEAD_ChemicalNerveAgent = 8300;  //
  WARHEAD_BiologicalGeneral = 9000;  //
};

//DetonatorFuze,Follow DIS Standerd
enum DetonatorFuzeEnum{
  FUZE_Other = 0;
  FUZE_Contact = 1000;  //
  FUZE_ContactInstant = 1100;  //
  FUZE_ContactDelayed = 1200;  //
  FUZE_Timed = 2000;  //
  FUZE_Proximity = 3000;  //
  FUZE_Command = 4000;  //
  FUZE_Altitude = 5000;  //
  FUZE_Depth = 6000;  //
  FUZE_Acoustic = 7000;  //
};

message FireEvent{
  string      attackerId = 1; //
  TrackID      targetTrack = 2; 
  Location_GEO    targetlocation = 3; // if targetId and target track is null,use this
  string      munitionId = 4; //
  string      eventId = 5;    //unique event id
  Location_GEO    Firelocation = 6;      //LLA
  string   munitionType = 7; //
  WarheadTypeEnum warheadType = 8; //
  DetonatorFuzeEnum fuseType = 9;   //
  uint32            quantity = 10;  //Number of shots in continuous fire.
  uint32            rate = 11;      //Rate of fire during continuous firing. per minute
  double       simTime = 12;
};

// 
message ffeTargetParam{
  string entityId = 1;
  TrackID targetTrackID = 2; //if this is null, use FireLocation
  Location_GEO    Firelocation = 3;
  string weaponName = 4;
  uint32 numberOfRounds = 5;
  float dispersionRadius = 6;
  float heightAboveTerrain = 7;
}

////////////////////////////////////////DetonationEvent///////////////////////////////////////////////////
//
enum DetonationResultEnum{
  DET_RES_NO_REASON = 0;
  DET_RES_PROXIMITY = 1;   //
  DET_RES_AGL_LIMIT = 2;   //
  DET_RES_MSL_LIMIT = 3;   //
  DET_RES_MIN_SPEED = 4;   //
  DET_RES_TOF_LIMIT = 5;   //
  DET_RES_COAST_TIME_LIMIT = 6;   //
  DET_RES_COMMAND = 7;   //
};

message DetonationEvent{
  string  attackerId = 1;     //
  string  targetId = 2;       //
  string  munitionId = 3;     //
  string  eventId = 4;        //event unique id
  Location_GEO    impactLocation = 5;   //LLA
  DetonationResultEnum result = 6; //
  string        munitionType = 7; //
  double       simTime = 8;
};

////////////////////////////////////////communication///////////////////////////////////////////////////
message MessageSendEvent{
  string       SenderId = 1;
  string       SenderComSystem = 2;
  string       messagetype = 3;
  string       ReceiverId = 4;
  string       ReceiverComSystem = 5; //can be null
  string       evtID = 6;
  double       simTime = 7;
};

message MessageRecvEvent{
  string       ReceiverId = 1;
  string       comSystemName = 2;
  string       messagetype = 3;
  string       evtID = 4;
  double       simTime = 5;
};

message MessageEvent
{
  oneof state{
    MessageSendEvent messageSendEvt = 1;
    MessageRecvEvent messageRecvEvt = 2;
  }
}

///////////////////////////////////entity Operation/////////////////////////////////
message EntityOptEvent{
  string       entityId = 1;
  bool         IsAddEvt = 2;//false when this is a Entity deleted Event;
  string       entiyType= 3 ; //only valid if IsAddEvt true
  string       evtID = 4;
  double       simTime = 5;
};


///////////////////////////////////damagefactor/////////////////////////////////
message DamageState {
  float    damagefactor = 1; //0-1
}

message TextMessage {
  string content = 1;
}

///////////////////////////////////SENSOR/////////////////////////////////
message SensorList {
  repeated SensorInfo sensorInfo = 1;
}

message SensorInfo{
  string name = 1;
  bool enable = 2;
}

message Points {
  Location_GEO point1 = 1;
  Location_GEO point2 = 2;
}

message Visibility {
  bool visibility = 1;
}

message SensorEvent{
  string entityID = 1;
  string sensorName = 2;
  bool FoundTarget = 3; //false represent lose target
  TrackID trackId = 4;
  string evtID = 5;
  double       simTime = 6;
}
